<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUILinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                           xmlns:app="http://schemas.android.com/apk/res-auto"
                                           android:layout_width="match_parent"
                                           android:layout_height="wrap_content"
                                           android:orientation="vertical"
                                           android:padding="24dp">

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="24dp"
        android:text="自定义尺寸"
        android:textColor="#333333"
        android:textSize="18sp"
        android:textStyle="bold"/>

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="请输入您需要的证件照尺寸（像素）"
        android:textColor="#666666"
        android:textSize="14sp"/>

    <!-- Width Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="宽度："
            android:textColor="#333333"
            android:textSize="16sp"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_width"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/edittext_border"
            android:gravity="center"
            android:hint="请输入宽度"
            android:inputType="number"
            android:maxLength="4"
            android:padding="12dp"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="16sp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="px"
            android:textColor="#666666"
            android:textSize="14sp"/>

    </LinearLayout>

    <!-- Height Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="高度："
            android:textColor="#333333"
            android:textSize="16sp"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_height"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/edittext_border"
            android:gravity="center"
            android:hint="请输入高度"
            android:inputType="number"
            android:maxLength="4"
            android:padding="12dp"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="16sp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="px"
            android:textColor="#666666"
            android:textSize="14sp"/>

    </LinearLayout>

    <!-- Title Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="名称："
            android:textColor="#333333"
            android:textSize="16sp"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_title"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/edittext_border"
            android:gravity="center"
            android:hint="请输入尺寸名称（可选）"
            android:inputType="text"
            android:maxLength="20"
            android:padding="12dp"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:textSize="16sp"/>

    </LinearLayout>

    <!-- Quick Preset Buttons -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="快速选择："
        android:textColor="#333333"
        android:textSize="14sp"
        android:textStyle="bold"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_preset_1inch"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginRight="4dp"
            android:layout_weight="1"
            android:text="一寸"
            android:textColor="@color/main_color"
            android:textSize="12sp"
            app:qmui_backgroundColor="#f8f9ff"
            app:qmui_borderColor="@color/main_color"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="4dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_preset_2inch"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:layout_weight="1"
            android:text="二寸"
            android:textColor="@color/main_color"
            android:textSize="12sp"
            app:qmui_backgroundColor="#f8f9ff"
            app:qmui_borderColor="@color/main_color"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="4dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_preset_id"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginLeft="4dp"
            android:layout_weight="1"
            android:text="身份证"
            android:textColor="@color/main_color"
            android:textSize="12sp"
            app:qmui_backgroundColor="#f8f9ff"
            app:qmui_borderColor="@color/main_color"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="4dp"/>

    </LinearLayout>

    <!-- Size Examples -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:background="#f8f9ff"
        android:padding="12dp"
        android:text="常见尺寸参考：\n• 一寸：295×413px\n• 二寸：413×550px\n• 身份证：358×441px"
        android:textColor="#666666"
        android:textSize="12sp"/>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginRight="8dp"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="#666666"
            android:textSize="16sp"
            app:qmui_borderColor="@color/main_color"
            app:qmui_backgroundColor="#f5f5f5"
            app:qmui_radius="6dp"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="8dp"
            android:layout_weight="1"
            android:text="确定"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:qmui_borderColor="@color/main_color"
            app:qmui_backgroundColor="@color/main_color"
            app:qmui_radius="6dp"/>

    </LinearLayout>

</com.qmuiteam.qmui.layout.QMUILinearLayout>
