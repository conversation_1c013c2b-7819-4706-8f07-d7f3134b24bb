<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent">

    <!-- 相纸尺寸选择区域 -->
    <LinearLayout
        android:id="@+id/paper_size_selector"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="#ffffff"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="相纸尺寸："
            android:textSize="14sp"
            android:textColor="#333333"
            android:layout_marginEnd="8dp"/>

        <com.qmuiteam.qmui.widget.QMUITabSegment
            android:id="@+id/paper_size_tabs"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/crop_line_switch_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="裁剪线"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="4dp"/>

        <Switch
            android:id="@+id/crop_line_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#f2f2f2"
        android:layout_gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
    >

        <ImageView
            android:id="@+id/crop_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"/>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>