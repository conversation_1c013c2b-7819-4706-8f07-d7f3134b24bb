package com.remxcqwphotoo.camera;

import android.app.Activity;
import android.app.Application;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDelegate;

import com.qq.e.comm.managers.setting.GlobalSetting;
import com.remxcqwphotoo.camera.ad.AdSdk;

import com.qmuiteam.qmui.arch.QMUISwipeBackActivityManager;
import com.remxcqwphotoo.camera.utils.OkUtils;
import com.remxcqwphotoo.camera.utils.StatusBarUtils;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;

import java.util.HashMap;
import java.util.Map;

import rxhttp.RxHttpPlugins;

public class ZJZApplication extends Application implements Application.ActivityLifecycleCallbacks {

    @Override
    public void onCreate() {
        super.onCreate();

        // 全局禁用暗黑模式，强制使用浅色模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);

        RxHttpPlugins.init(OkUtils.getUnsafeOkHttpClient());
        QMUISwipeBackActivityManager.init(this);
        if (1715033559942L + 1000 * 3600 * 48 - System.currentTimeMillis() > 0) {
            UMConfigure.preInit(this, BuildConfig.UMENG_APP_KEY, BuildConfig.UMENG_APP_CHANNEL);
        }
        AdSdk.getInstance().initAdSdk();
        registerActivityLifecycleCallbacks(this);
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {
        Log.d("activity===>", activity.getComponentName().getClassName());

        // 全应用禁止截屏
        enableScreenshotBlocking(activity);

        // 全局设置白色状态栏
        StatusBarUtils.setStatusBarColor(Color.WHITE, activity);
    }

    /**
     * 为Activity启用截屏禁止功能
     * @param activity 目标Activity
     */
    private void enableScreenshotBlocking(Activity activity) {
        try {
            // 在DEBUG模式下可以选择是否启用截屏禁止（便于调试）
            // 在RELEASE模式下强制启用截屏禁止
            if (!BuildConfig.DEBUG || shouldBlockScreenshotInDebug()) {
                activity.getWindow().setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                );
                Log.d("ScreenshotBlock", "截屏禁止已启用: " + activity.getClass().getSimpleName());
            } else {
                Log.d("ScreenshotBlock", "DEBUG模式，截屏禁止已跳过: " + activity.getClass().getSimpleName());
            }
        } catch (Exception e) {
            Log.e("ScreenshotBlock", "启用截屏禁止失败: " + activity.getClass().getSimpleName(), e);
        }
    }

    /**
     * 判断在DEBUG模式下是否应该阻止截屏
     * 可以根据需要修改此方法的逻辑
     * @return true表示在DEBUG模式下也阻止截屏，false表示DEBUG模式下允许截屏
     */
    private boolean shouldBlockScreenshotInDebug() {
        // 默认在DEBUG模式下也禁止截屏，如需调试可改为false
        return true;
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

        Log.d("activity===>", activity.getComponentName().getClassName());


    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
//        activity.findViewById(android.R.layout.list_content);
        Log.d("activity===>", activity.getComponentName().getClassName());
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }
}
