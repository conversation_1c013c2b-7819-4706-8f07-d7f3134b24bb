package com.remxcqwphotoo.camera;

import com.remxcqwphotoo.camera.model.PicSize;

import java.util.ArrayList;
import java.util.List;

public class Const {
    //    public static List<PicSize> picSizeList = new ArrayList<>();
    public static List<PicSize> allPicList = new ArrayList<>();

//    static {
//        picSizeList.add(new PicSize(295, 413, "一寸", "适用：学生证、简历照"));
//        picSizeList.add(new PicSize(260, 378, "小一寸", "适用：驾照、报名照"));
//        picSizeList.add(new PicSize(413, 579, "二寸", "适用：通行证照"));
//        picSizeList.add(new PicSize(390, 567, "小二寸", "适用：职业考证照"));
//        picSizeList.add(new PicSize(531, 708, "研究生考试（4：3）", "适用：职业考证照"));
//    }

    static {


        // 基础证件照尺寸
        allPicList.add(new PicSize(295, 413, "一寸", "25mm*35mm，适用：学生证、工作证、简历、常规考试"));
        allPicList.add(new PicSize(413, 579, "二寸", "35mm*49mm，适用：毕业证、学位证（部分）、签证（部分）"));
        allPicList.add(new PicSize(390, 567, "小二寸/护照照片", "33mm*48mm，适用：中国护照、各国签证、各类资格考试"));
        allPicList.add(new PicSize(260, 378, "小一寸", "22mm*32mm，适用：驾驶证、居住证"));

// 身份证件类
        allPicList.add(new PicSize(358, 441, "身份证", "26mm*32mm，公安部标准，适用：二代身份证、社保卡")); // 身份证尺寸标准为 26mm*32mm，但公安系统上传标准为 358*441 像素，此为特殊规定，予以保留。
        allPicList.add(new PicSize(260, 378, "驾驶证", "22mm*32mm，适用：机动车驾驶证"));
        allPicList.add(new PicSize(390, 567, "护照", "33mm*48mm，适用：中国护照、旅行证")); // 修正：护照尺寸统一为官方标准 390x567px (33x48mm)。
        allPicList.add(new PicSize(390, 567, "港澳通行证", "33mm*48mm，适用：往来港澳通行证")); // 修正：与护照尺寸统一。
        allPicList.add(new PicSize(390, 567, "台湾通行证", "33mm*48mm，适用：大陆居民往来台湾通行证")); // 修正：与护照尺寸统一。
        allPicList.add(new PicSize(260, 378, "居住证", "22mm*32mm，适用：居住证、暂住证")); // 修正：居住证通常采用小一寸或身份证标准，小一寸更常见。

// 教育考试类
        allPicList.add(new PicSize(480, 640, "高考报名(3:4)", "适用：高考、中考报名")); // 修正描述：增加比例，更清晰。
        allPicList.add(new PicSize(390, 567, "研究生考试", "适用：全国硕士研究生统一招生考试（研招网）"));
        allPicList.add(new PicSize(144, 192, "普通话水平测试", "适用：全国普通话等级证书（部分地区要求）")); // 修正：此小尺寸更具代表性，但各地要求不一。
        allPicList.add(new PicSize(295, 413, "英语四六级(CET)", "适用：全国大学英语四、六级考试")); // 修正描述：增加(CET)。
        allPicList.add(new PicSize(295, 413, "学位证", "适用：学士、硕士、博士学位证")); // 修正：学位证尺寸不统一，一寸和二寸都有，此处保留一寸作为通用。
        allPicList.add(new PicSize(295, 413, "学生证", "适用：各类学生证、校园卡"));

// 职业资格与技能考试
        allPicList.add(new PicSize(295, 413, "公务员考试", "适用：国家公务员、地方公务员考试")); // 修正：公务员考试多要求标准一寸照。原尺寸413x513不常见。
        allPicList.add(new PicSize(295, 413, "教师资格证", "适用：中小学教师资格考试"));
        allPicList.add(new PicSize(295, 413, "会计资格证", "适用：初级、中级、高级会计师、注册会计师(CPA)"));
        allPicList.add(new PicSize(390, 567, "司法考试", "适用：国家统一法律职业资格考试"));
        allPicList.add(new PicSize(295, 413, "医师/护士资格证", "适用：执业医师、护士执业资格考试")); // 合并：医师和护士资格证要求通常相同。
        allPicList.add(new PicSize(295, 413, "计算机等级考试", "适用：全国计算机等级考试(NCRE)"));
        allPicList.add(new PicSize(295, 413, "建造师/工程师系列", "适用：建造师、造价、消防、监理等工程师类考试")); // 合并：这类工程类证书考试通常都使用标准一寸照。

// 社会保障与求职
        allPicList.add(new PicSize(358, 441, "社保卡/医保卡", "适用：社会保障卡、医疗保障卡")); // 修正：社保卡照片规范与身份证一致。
        allPicList.add(new PicSize(295, 413, "简历照片", "适用：个人求职简历"));
        allPicList.add(new PicSize(295, 413, "工作证/上岗证", "适用：员工工作证、从业上岗证"));

// 特殊用途
        allPicList.add(new PicSize(531, 354, "结婚证", "红色背景，6cm*4cm，适用：结婚登记")); // 修正：结婚证为横版照片，尺寸为 6x4cm，即 709x472px，此处按比例调整为 531x354。原数据为一寸照，不正确。
        allPicList.add(new PicSize(295, 413, "健康证", "适用：从业人员健康证明"));

// 国际签证（重要修正）
        allPicList.add(new PicSize(600, 600, "美国签证/护照", "2*2英寸(51mm*51mm)")); // 修正：美国签证和护照要求为 2x2英寸(51x51mm)的方形照片，即 600x600px。
        allPicList.add(new PicSize(413, 531, "申根签证(欧盟)", "35mm*45mm，适用：德国、法国、意大利等申根区国家")); // 修正：欧盟申根签证标准尺寸为 35x45mm，即 413x531px。
        allPicList.add(new PicSize(413, 531, "日本签证", "35mm*45mm，与申根签尺寸相同")); // 修正：日本签证同样要求 35x45mm。
        allPicList.add(new PicSize(413, 531, "韩国签证", "35mm*45mm，与申根签尺寸相同")); // 修正：韩国签证同样要求 35x45mm。
        allPicList.add(new PicSize(390, 567, "加拿大签证", "33mm*48mm，照片头部尺寸要求严格")); // 修正：加拿大签证照片物理尺寸为50x70mm，但线上提交时常裁剪为类似护照照的头部区域，且各渠道要求不一。为方便使用，此处提供一个常用电子版规格，但强烈建议按官方最新指引操作。

    }
}
/// / 基础证件照尺寸
//        allPicList.add(new PicSize(295, 413, "一寸", "25mm*35mm，适用：学生证、工作证、简历、常规考试"));
//        allPicList.add(new PicSize(413, 579, "二寸", "35mm*49mm，适用：毕业证、学位证（部分）、签证（部分）"));
//        allPicList.add(new PicSize(390, 567, "小二寸/护照照片", "33mm*48mm，适用：中国护照、各国签证、各类资格考试"));
//        allPicList.add(new PicSize(260, 378, "小一寸", "22mm*32mm，适用：驾驶证、居住证"));
//
//// 身份证件类
//        allPicList.add(new PicSize(358, 441, "身份证", "26mm*32mm，公安部标准，适用：二代身份证、社保卡")); // 身份证尺寸标准为 26mm*32mm，但公安系统上传标准为 358*441 像素，此为特殊规定，予以保留。
//        allPicList.add(new PicSize(260, 378, "驾驶证", "22mm*32mm，适用：机动车驾驶证"));
//        allPicList.add(new PicSize(390, 567, "护照", "33mm*48mm，适用：中国护照、旅行证")); // 修正：护照尺寸统一为官方标准 390x567px (33x48mm)。
//        allPicList.add(new PicSize(390, 567, "港澳通行证", "33mm*48mm，适用：往来港澳通行证")); // 修正：与护照尺寸统一。
//        allPicList.add(new PicSize(390, 567, "台湾通行证", "33mm*48mm，适用：大陆居民往来台湾通行证")); // 修正：与护照尺寸统一。
//        allPicList.add(new PicSize(260, 378, "居住证", "22mm*32mm，适用：居住证、暂住证")); // 修正：居住证通常采用小一寸或身份证标准，小一寸更常见。
//
//// 教育考试类
//        allPicList.add(new PicSize(480, 640, "高考报名(3:4)", "适用：高考、中考报名")); // 修正描述：增加比例，更清晰。
//        allPicList.add(new PicSize(390, 567, "研究生考试", "适用：全国硕士研究生统一招生考试（研招网）"));
//        allPicList.add(new PicSize(144, 192, "普通话水平测试", "适用：全国普通话等级证书（部分地区要求）")); // 修正：此小尺寸更具代表性，但各地要求不一。
//        allPicList.add(new PicSize(295, 413, "英语四六级(CET)", "适用：全国大学英语四、六级考试")); // 修正描述：增加(CET)。
//        allPicList.add(new PicSize(295, 413, "学位证", "适用：学士、硕士、博士学位证")); // 修正：学位证尺寸不统一，一寸和二寸都有，此处保留一寸作为通用。
//        allPicList.add(new PicSize(295, 413, "学生证", "适用：各类学生证、校园卡"));
//
//// 职业资格与技能考试
//        allPicList.add(new PicSize(295, 413, "公务员考试", "适用：国家公务员、地方公务员考试")); // 修正：公务员考试多要求标准一寸照。原尺寸413x513不常见。
//        allPicList.add(new PicSize(295, 413, "教师资格证", "适用：中小学教师资格考试"));
//        allPicList.add(new PicSize(295, 413, "会计资格证", "适用：初级、中级、高级会计师、注册会计师(CPA)"));
//        allPicList.add(new PicSize(390, 567, "司法考试", "适用：国家统一法律职业资格考试"));
//        allPicList.add(new PicSize(295, 413, "医师/护士资格证", "适用：执业医师、护士执业资格考试")); // 合并：医师和护士资格证要求通常相同。
//        allPicList.add(new PicSize(295, 413, "计算机等级考试", "适用：全国计算机等级考试(NCRE)"));
//        allPicList.add(new PicSize(295, 413, "建造师/工程师系列", "适用：建造师、造价、消防、监理等工程师类考试")); // 合并：这类工程类证书考试通常都使用标准一寸照。
//
//// 社会保障与求职
//        allPicList.add(new PicSize(358, 441, "社保卡/医保卡", "适用：社会保障卡、医疗保障卡")); // 修正：社保卡照片规范与身份证一致。
//        allPicList.add(new PicSize(295, 413, "简历照片", "适用：个人求职简历"));
//        allPicList.add(new PicSize(295, 413, "工作证/上岗证", "适用：员工工作证、从业上岗证"));
//
//// 特殊用途
//        allPicList.add(new PicSize(531, 354, "结婚证", "红色背景，6cm*4cm，适用：结婚登记")); // 修正：结婚证为横版照片，尺寸为 6x4cm，即 709x472px，此处按比例调整为 531x354。原数据为一寸照，不正确。
//        allPicList.add(new PicSize(295, 413, "健康证", "适用：从业人员健康证明"));
//
//// 国际签证（重要修正）
//        allPicList.add(new PicSize(600, 600, "美国签证/护照", "2*2英寸(51mm*51mm)")); // 修正：美国签证和护照要求为 2x2英寸(51x51mm)的方形照片，即 600x600px。
//        allPicList.add(new PicSize(413, 531, "申根签证(欧盟)", "35mm*45mm，适用：德国、法国、意大利等申根区国家")); // 修正：欧盟申根签证标准尺寸为 35x45mm，即 413x531px。
//        allPicList.add(new PicSize(413, 531, "日本签证", "35mm*45mm，与申根签尺寸相同")); // 修正：日本签证同样要求 35x45mm。
//        allPicList.add(new PicSize(413, 531, "韩国签证", "35mm*45mm，与申根签尺寸相同")); // 修正：韩国签证同样要求 35x45mm。
//        allPicList.add(new PicSize(390, 567, "加拿大签证", "33mm*48mm，照片头部尺寸要求严格")); // 修正：加拿大签证照片物理尺寸为50x70mm，但线上提交时常裁剪为类似护照照的头部区域，且各渠道要求不一。为方便使用，此处提供一个常用电子版规格，但强烈建议按官方最新指引操作。
//    }

//}
