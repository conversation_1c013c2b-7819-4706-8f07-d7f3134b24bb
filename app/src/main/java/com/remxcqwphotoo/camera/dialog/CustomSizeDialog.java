package com.remxcqwphotoo.camera.dialog;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;

import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.remxcqwphotoo.camera.R;
import com.remxcqwphotoo.camera.model.PicSize;

public class CustomSizeDialog {

    public interface OnCustomSizeListener {
        void onCustomSizeConfirmed(PicSize customSize);
        void onCustomSizeCancelled();
    }

    private Context context;
    private OnCustomSizeListener listener;
    private QMUIDialog dialog;
    private SharedPreferences preferences;

    private static final String PREF_NAME = "custom_size_prefs";
    private static final String KEY_LAST_WIDTH = "last_width";
    private static final String KEY_LAST_HEIGHT = "last_height";
    private static final String KEY_LAST_TITLE = "last_title";

    public CustomSizeDialog(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public void setOnCustomSizeListener(OnCustomSizeListener listener) {
        this.listener = listener;
    }

    public void show() {

        dialog = new QMUIDialog.CustomDialogBuilder(context)
                .setLayout(R.layout.dialog_custom_size)
                .create();




        EditText etWidth = dialog.findViewById(R.id.et_width);
        EditText etHeight = dialog.findViewById(R.id.et_height);
        EditText etTitle = dialog.findViewById(R.id.et_title);
        QMUIRoundButton btnCancel = dialog.findViewById(R.id.btn_cancel);
        QMUIRoundButton btnConfirm = dialog.findViewById(R.id.btn_confirm);
        QMUIRoundButton btnPreset1inch = dialog.findViewById(R.id.btn_preset_1inch);
        QMUIRoundButton btnPreset2inch = dialog.findViewById(R.id.btn_preset_2inch);
        QMUIRoundButton btnPresetId = dialog.findViewById(R.id.btn_preset_id);
        // Load last used values
        loadLastUsedValues(etWidth, etHeight, etTitle);
        // Preset button listeners
        btnPreset1inch.setOnClickListener(v -> {
            etWidth.setText("295");
            etHeight.setText("413");
            etTitle.setText("一寸");
        });

        btnPreset2inch.setOnClickListener(v -> {
            etWidth.setText("413");
            etHeight.setText("550");
            etTitle.setText("二寸");
        });

        btnPresetId.setOnClickListener(v -> {
            etWidth.setText("358");
            etHeight.setText("441");
            etTitle.setText("身份证");
        });

        btnCancel.setOnClickListener(v -> {
            dialog.dismiss();
            if (listener != null) {
                listener.onCustomSizeCancelled();
            }
        });

        btnConfirm.setOnClickListener(v -> {
            String widthStr = etWidth.getText().toString().trim();
            String heightStr = etHeight.getText().toString().trim();
            String title = etTitle.getText().toString().trim();

            if (validateInput(widthStr, heightStr)) {
                int width = Integer.parseInt(widthStr);
                int height = Integer.parseInt(heightStr);
                
                // 如果没有输入标题，使用默认格式
                if (TextUtils.isEmpty(title)) {
                    title = "自定义尺寸";
                }
                
                String description = String.format("自定义尺寸 %d×%d", width, height);
                PicSize customSize = new PicSize(width, height, title, description);

                // Save the custom size for next time
                saveLastUsedValues(width, height, title);

                dialog.dismiss();
                if (listener != null) {
                    listener.onCustomSizeConfirmed(customSize);
                }
            }
        });

        dialog.show();
    }

    private boolean validateInput(String widthStr, String heightStr) {
        // 检查是否为空
        if (TextUtils.isEmpty(widthStr) || TextUtils.isEmpty(heightStr)) {
            showError("请输入完整的宽度和高度");
            return false;
        }

        try {
            int width = Integer.parseInt(widthStr);
            int height = Integer.parseInt(heightStr);

            // 检查尺寸范围
            if (width < 50 || width > 800) {
                showError("宽度必须在50-800像素之间");
                return false;
            }

            if (height < 50 || height > 800) {
                showError("高度必须在50-800像素之间");
                return false;
            }

            // 检查比例是否合理（避免过于极端的比例）
            double ratio = Math.max(width, height) / (double) Math.min(width, height);
            if (ratio > 10) {
                showError("宽高比例过于极端，请输入合理的尺寸");
                return false;
            }

            return true;

        } catch (NumberFormatException e) {
            showError("请输入有效的数字");
            return false;
        }
    }

    private void showError(String message) {
        new QMUIDialog.MessageDialogBuilder(context)
                .setTitle("输入错误")
                .setMessage(message)
                .addAction("确定", (dialog, index) -> dialog.dismiss())
                .create()
                .show();
    }

    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    /**
     * Load last used custom size values
     */
    private void loadLastUsedValues(EditText etWidth, EditText etHeight, EditText etTitle) {
        String lastWidth = preferences.getString(KEY_LAST_WIDTH, "");
        String lastHeight = preferences.getString(KEY_LAST_HEIGHT, "");
        String lastTitle = preferences.getString(KEY_LAST_TITLE, "");

        if (!TextUtils.isEmpty(lastWidth)) {
            etWidth.setText(lastWidth);
        }
        if (!TextUtils.isEmpty(lastHeight)) {
            etHeight.setText(lastHeight);
        }
        if (!TextUtils.isEmpty(lastTitle)) {
            etTitle.setText(lastTitle);
        }
    }

    /**
     * Save custom size values for next time
     */
    private void saveLastUsedValues(int width, int height, String title) {
        preferences.edit()
                .putString(KEY_LAST_WIDTH, String.valueOf(width))
                .putString(KEY_LAST_HEIGHT, String.valueOf(height))
                .putString(KEY_LAST_TITLE, title)
                .apply();
    }
}
