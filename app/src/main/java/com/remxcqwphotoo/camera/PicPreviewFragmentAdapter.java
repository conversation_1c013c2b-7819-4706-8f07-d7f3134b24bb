package com.remxcqwphotoo.camera;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.remxcqwphotoo.camera.model.PicSize;

import java.util.HashMap;
import java.util.Map;

class PicPreviewFragmentAdapter extends FragmentPagerAdapter {
    private PicSize size;
    private String filePath;
    private boolean isOriginalStyle;
    private Map<Integer, SinglePicFragment> fragmentMap = new HashMap<>();

    public PicPreviewFragmentAdapter(@NonNull FragmentManager fm, PicSize size, String filePath) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        this.size = size;
        this.filePath = filePath;
        this.isOriginalStyle = false;
    }
    
    public PicPreviewFragmentAdapter(@NonNull FragmentManager fm, PicSize size, String filePath, boolean isOriginalStyle) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        this.size = size;
        this.filePath = filePath;
        this.isOriginalStyle = isOriginalStyle;
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        SinglePicFragment fragment;
        if (position == 0) {
            fragment = SinglePicFragment.newInstance(1, filePath, size, isOriginalStyle);
        } else {
            fragment = SinglePicFragment.newInstance(2, filePath, size, isOriginalStyle);
        }
        fragmentMap.put(position, fragment);
        return fragment;
    }

    @Override
    public int getCount() {
        return 2;
    }

    /**
     * 获取已创建的Fragment
     */
    public SinglePicFragment getCreatedFragment(int position) {
        return fragmentMap.get(position);
    }
}
