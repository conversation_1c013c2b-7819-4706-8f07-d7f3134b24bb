package com.remxcqwphotoo.camera;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.dialog.ColorPickerDialog;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.mgr.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import com.qmuiteam.qmui.widget.QMUITopBar;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.remxcqwphotoo.camera.utils.StatusBarUtils;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
//import rxhttp.wrapper.param.RxHttp;

import static com.qmuiteam.qmui.widget.dialog.QMUITipDialog.Builder.ICON_TYPE_LOADING;

import java.io.File;


public class PicturePreviewActivity extends AppCompatActivity implements View.OnClickListener {
    public final static String PICK_IMG = "PICK_IMG";
    private String resultPath;
    private CompositeDisposable disposables = new CompositeDisposable();

    // 选中指示器ID数组（对应选中状态的ImageView）
    private int[] indicatorIds = {R.id.iv_white, R.id.iv_blue, R.id.iv_red, R.id.iv_gray,
            R.id.iv_light_blue, R.id.iv_dark_blue, R.id.iv_pink, R.id.iv_green, R.id.iv_custom, R.id.iv_original};

    // 容器ID数组（对应点击区域的LinearLayout）
    private int[] containerIds = {R.id.ll_white, R.id.ll_blue, R.id.ll_red, R.id.ll_gray,
            R.id.ll_light_blue, R.id.ll_dark_blue, R.id.ll_pink, R.id.ll_green, R.id.ll_custom, R.id.ll_original};

    private ImageView cropImageView;
    private Bitmap originBitmap = null; // 透明背景的证件照bitmap
    private Bitmap croppedBitmap = null; // 原始裁剪的bitmap（用于原始样式）
    private Bitmap destBitmap = null;
    private QMUITipDialog tipsDialog = null;
    private View customColorView;

    int curPos = 9; // 默认选中原始样式
    private int customColor = Color.rgb(204, 204, 204); // 默认自定义颜色
    private boolean isOriginalStyle = true; // 默认为原始样式

    // 预定义颜色数组
    private final int[] predefinedColors = {
            Color.rgb(255, 255, 255), // 白色
            Color.rgb(67, 142, 219),  // 蓝色
            Color.rgb(255, 0, 0),     // 红色
            Color.rgb(128, 128, 128), // 灰色
            Color.rgb(135, 206, 235), // 浅蓝色
            Color.rgb(30, 58, 138),   // 深蓝色
            Color.rgb(255, 192, 203), // 粉色
            Color.rgb(50, 205, 50),   // 绿色
            -1, // 自定义颜色标识
            -2  // 原始样式标识
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 截屏禁止功能已在Application中统一处理，此处移除重复代码
        // 状态栏颜色已在Application中统一处理，此处移除重复代码
        setContentView(R.layout.activity_picture_preview);
        cropImageView = findViewById(R.id.cropImageView);
        customColorView = findViewById(R.id.view_custom_color);
        initView();
        resultPath = getIntent().getStringExtra(PICK_IMG);
        // 同时生成两种bitmap
        generateBothBitmaps();
        AdSdk.getInstance().showInterAd(this);
    }

    /**
     * 同时生成透明背景证件照和原始裁剪照片
     */
    private void generateBothBitmaps() {
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");

        if (tipsDialog == null) {
            tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                    .setIconType(ICON_TYPE_LOADING)
                    .setTipWord("正在处理请耐心等待...")
                    .create();
        }
        tipsDialog.show();

        // 先生成透明背景证件照
        IdPhotoManager.getInstance().generateIdPhotoWithFallback(this, resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                // 已经显示了loading
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                originBitmap = bitmap;
                // 继续生成原始裁剪照片
                generateCroppedPhoto(picSize);
            }

            @Override
            public void onError(String error) {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                ToastUtils.showShort("生成证件照失败: " + error);
            }

            @Override
            public void onComplete() {
                // 在generateCroppedPhoto中处理完成逻辑
            }
        });
    }

    /**
     * 生成原始裁剪照片
     */
    private void generateCroppedPhoto(PicSize picSize) {
        IdPhotoManager.getInstance().generateCroppedIdPhoto(resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                // 已经显示了loading
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                croppedBitmap = bitmap;
                // 两种bitmap都准备好了，显示默认的原始样式
                updateSelectedState();
                displayCurrentImage();
            }

            @Override
            public void onError(String error) {
                // 如果原始裁剪失败，使用透明背景的作为备用
                croppedBitmap = originBitmap;
                updateSelectedState();
                displayCurrentImage();
            }

            @Override
            public void onComplete() {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
            }
        });
    }

    /**
     * 仅生成证件照（带完整fallback策略）
     */
    private void generateIdPhotoOnly() {
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");

        // 使用带fallback的证件照生成
        IdPhotoManager.getInstance().generateIdPhotoWithFallback(this, resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (tipsDialog == null) {
                    tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                            .setIconType(ICON_TYPE_LOADING)
                            .setTipWord("正在处理请耐心等待...")
                            .create();
                }
                tipsDialog.show();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                originBitmap = bitmap;
                updateSelectedState();
                displayCurrentImage();
            }

            @Override
            public void onError(String error) {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                ToastUtils.showShort("所有处理方案都失败了: " + error);
            }

            @Override
            public void onComplete() {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
            }
        });
    }

    /**
     * 获取原始样式照片（如果已有则直接使用）
     */
    private void getOriginalStylePhoto() {
        if (croppedBitmap != null) {
            // 如果已经有原始裁剪bitmap，直接使用
            isOriginalStyle = true;
            curPos = 9;
            updateSelectedState();
            displayCurrentImage();
        } else {
            // 如果没有，重新生成
            PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");

            if (tipsDialog == null) {
                tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                        .setIconType(ICON_TYPE_LOADING)
                        .setTipWord("正在处理请耐心等待...")
                        .create();
            }
            tipsDialog.show();

            IdPhotoManager.getInstance().generateCroppedIdPhoto(resultPath, picSize, new ApiCallback<Bitmap>() {
                @Override
                public void onStart() {
                    // 已经显示了loading
                }

                @Override
                public void onSuccess(Bitmap bitmap) {
                    croppedBitmap = bitmap;
                    isOriginalStyle = true;
                    curPos = 9;
                    updateSelectedState();
                    displayCurrentImage();
                }

                @Override
                public void onError(String error) {
                    if (tipsDialog != null) {
                        tipsDialog.dismiss();
                    }
                    ToastUtils.showShort("获取原始样式失败: " + error);
                    // 失败时回退到白色背景
                    changePos(0);
                }

                @Override
                public void onComplete() {
                    if (tipsDialog != null) {
                        tipsDialog.dismiss();
                    }
                }
            });
        }
    }

    /**
     * 更新选中状态指示器
     */
    private void updateSelectedState() {
        // 更新选中指示器的可见性
        for (int i = 0; i < indicatorIds.length; i++) {
            findViewById(indicatorIds[i]).setVisibility(curPos == i ? View.VISIBLE : View.INVISIBLE);
        }
    }

    /**
     * 显示当前选中的图片
     */
    private void displayCurrentImage() {
        if (isOriginalStyle && croppedBitmap != null) {
            // 显示原始样式
            cropImageView.setImageBitmap(croppedBitmap);
        } else if (!isOriginalStyle && originBitmap != null) {
            // 显示带背景颜色的样式
            drawImageWithBackground(curPos);
        }
    }

    /**
     * 绘制带背景颜色的图片
     */
    private void drawImageWithBackground(int colorPos) {
        if (originBitmap == null) {
            ToastUtils.showShort("数据解析失败");
            return;
        }

        int selectColor;
        if (colorPos < predefinedColors.length - 2) {
            // 使用预定义颜色
            selectColor = predefinedColors[colorPos];
        } else if (colorPos == predefinedColors.length - 2) {
            // 使用自定义颜色
            selectColor = customColor;
        } else {
            // 不应该到这里，因为原始样式在displayCurrentImage中已经处理
            return;
        }

        // 生成带背景的图片
        int bH = originBitmap.getHeight();
        int bW = originBitmap.getWidth();
        Bitmap canvasBitmap = Bitmap.createBitmap(bW, bH, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(canvasBitmap);
        Paint paint = new Paint();
        paint.setColor(selectColor);
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(0, 0, bW, bH, paint);
        canvas.drawBitmap(originBitmap, new Rect(0, 0, bW, bH), new Rect(0, 0, bW, bH), paint);

        // 显示预览图片
        cropImageView.setImageBitmap(canvasBitmap);
    }

    public void drawImage(int colorPos, boolean isNext) {
        // 更新状态
        curPos = colorPos;
        isOriginalStyle = (colorPos == 9);

        // 显示图片
        displayCurrentImage();

        // 只有在点击"下一步"时才生成最终图片
        if (isNext) {
            generateFinalBitmap();
        }
    }

    /**
     * 生成最终用于保存的bitmap
     */
    private void generateFinalBitmap() {
        if (isOriginalStyle && croppedBitmap != null) {
            destBitmap = croppedBitmap.copy(Bitmap.Config.ARGB_8888, false);
        } else if (!isOriginalStyle && originBitmap != null) {
            int selectColor;
            if (curPos < predefinedColors.length - 2) {
                selectColor = predefinedColors[curPos];
            } else if (curPos == predefinedColors.length - 2) {
                selectColor = customColor;
            } else {
                return;
            }

            int bH = originBitmap.getHeight();
            int bW = originBitmap.getWidth();
            destBitmap = Bitmap.createBitmap(bW, bH, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(destBitmap);
            Paint paint = new Paint();
            paint.setColor(selectColor);
            paint.setStyle(Paint.Style.FILL);
            canvas.drawRect(0, 0, bW, bH, paint);
            canvas.drawBitmap(originBitmap, new Rect(0, 0, bW, bH), new Rect(0, 0, bW, bH), paint);
        }
    }

    private void initView() {
        QMUITopBar topBar = findViewById(R.id.top_bar);
        topBar.setTitle("调整背景颜色");
        topBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        // 设置点击监听器
        findViewById(R.id.btn_next).setOnClickListener(this);
        findViewById(R.id.ll_white).setOnClickListener(this);
        findViewById(R.id.ll_blue).setOnClickListener(this);
        findViewById(R.id.ll_red).setOnClickListener(this);
        findViewById(R.id.ll_gray).setOnClickListener(this);
        findViewById(R.id.ll_light_blue).setOnClickListener(this);
        findViewById(R.id.ll_dark_blue).setOnClickListener(this);
        findViewById(R.id.ll_pink).setOnClickListener(this);
        findViewById(R.id.ll_green).setOnClickListener(this);
        findViewById(R.id.ll_custom).setOnClickListener(this);
        findViewById(R.id.ll_original).setOnClickListener(this);

        // 更新自定义颜色显示
        updateCustomColorView();

        // 设置默认选中状态（原始样式）
        updateSelectedState();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposables.dispose();
        if (destBitmap != null && !destBitmap.isRecycled()) {
            destBitmap.recycle();
        }
        if (originBitmap != null && !originBitmap.isRecycled()) {
            originBitmap.recycle();
        }
        if (croppedBitmap != null && !croppedBitmap.isRecycled()) {
            croppedBitmap.recycle();
        }
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btn_next) {
            getNext();
        } else if (id == R.id.ll_custom) {
            showColorPicker();
        } else if (id == R.id.ll_original) {
            getOriginalStylePhoto();
        } else {
            for (int i = 0; i < containerIds.length; i++) {
                if (id == containerIds[i]) {
                    changePos(i);
                    break;
                }
            }
        }
    }

    private void getNext() {
        drawImage(curPos, true);
        String filePath = ImageUtils.save2Album(destBitmap == null ? BitmapFactory.decodeFile(resultPath) : destBitmap, Bitmap.CompressFormat.JPEG).getPath();
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
        Intent intent = new Intent(PicturePreviewActivity.this, SavePicActivity.class);
        intent.putExtra(PICK_IMG, filePath);
        intent.putExtra("SIZE", picSize);
        intent.putExtra("IS_ORIGINAL_STYLE", isOriginalStyle);
        startActivity(intent);
    }

    /**
     * 切换选中的背景颜色位置
     */
    private void changePos(int pos) {
        // 更新当前位置和样式状态
        curPos = pos;
        isOriginalStyle = (pos == 9);

        // 更新选中指示器
        updateSelectedState();

        if (pos == 9) {
            // 切换到原始样式
            if (croppedBitmap != null) {
                // 如果已有原始bitmap，直接显示
                displayCurrentImage();
            } else {
                // 如果没有，重新生成
                getOriginalStylePhoto();
            }
        } else {
            // 切换到背景颜色样式
            if (originBitmap != null) {
                // 如果已有透明背景bitmap，直接显示
                displayCurrentImage();
            } else {
                // 如果没有，重新生成
                generateIdPhotoOnly();
            }
        }
    }

    /**
     * 显示颜色选择器对话框
     */
    private void showColorPicker() {
        // 保存当前颜色，以便取消时恢复
        final int originalColor = customColor;
        final int originalPos = curPos;

        ColorPickerDialog dialog = new ColorPickerDialog(this);
        dialog.setColor(customColor);

        // 设置实时颜色变化监听器
        dialog.setOnColorChangedListener(new ColorPickerDialog.OnColorChangedListener() {
            @Override
            public void onColorChanged(int color) {
                // 实时更新自定义颜色并预览
                customColor = color;
                updateCustomColorView();
                // 实时预览颜色效果（临时切换到自定义颜色）
                int tempPos = curPos;
                boolean tempOriginalStyle = isOriginalStyle;
                curPos = 8;
                isOriginalStyle = false;
                displayCurrentImage();
                // 恢复状态（但不更新显示）
                curPos = tempPos;
                isOriginalStyle = tempOriginalStyle;
            }
        });

        // 设置最终确认监听器
        dialog.setOnColorSelectedListener(new ColorPickerDialog.OnColorSelectedListener() {
            @Override
            public void onColorSelected(int color) {
                customColor = color;
                updateCustomColorView();
                changePos(8); // 自定义颜色位置，更新选中状态
            }
        });

        // 设置取消监听器，恢复原来的颜色
        dialog.setOnCancelListener(dialogInterface -> {
            customColor = originalColor;
            updateCustomColorView();
            // 恢复原来的预览
            curPos = originalPos;
            isOriginalStyle = (originalPos == 9);
            updateSelectedState();
            displayCurrentImage();
        });

        dialog.show();
    }

    /**
     * 更新自定义颜色显示
     */
    private void updateCustomColorView() {
        if (customColorView != null) {
            GradientDrawable drawable = new GradientDrawable();
            drawable.setShape(GradientDrawable.RECTANGLE);
            drawable.setColor(customColor);
            drawable.setCornerRadius(40f);
            drawable.setStroke(4, Color.parseColor("#666666"));
            customColorView.setBackground(drawable);
        }
    }

}
