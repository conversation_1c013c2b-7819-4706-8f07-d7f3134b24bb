package com.remxcqwphotoo.camera;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.dialog.ColorPickerDialog;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.mgr.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import com.qmuiteam.qmui.widget.QMUITopBar;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.remxcqwphotoo.camera.utils.StatusBarUtils;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
//import rxhttp.wrapper.param.RxHttp;

import static com.qmuiteam.qmui.widget.dialog.QMUITipDialog.Builder.ICON_TYPE_LOADING;

import java.io.File;


public class PicturePreviewActivity extends AppCompatActivity implements View.OnClickListener {
    public final static String PICK_IMG = "PICK_IMG";
    private String resultPath;
    private CompositeDisposable disposables = new CompositeDisposable();

    // 扩展颜色选择ID数组
    private int[] ids = {R.id.iv_white, R.id.iv_blue, R.id.iv_red, R.id.iv_gray,
            R.id.iv_light_blue, R.id.iv_dark_blue, R.id.iv_pink, R.id.iv_green, R.id.iv_custom, R.id.iv_original};

    private ImageView cropImageView;
    private Bitmap originBitmap = null;
    private Bitmap destBitmap = null;
    private QMUITipDialog tipsDialog = null;
    private View customColorView;

    int curPos = 0;
    private int customColor = Color.rgb(204, 204, 204); // 默认自定义颜色
    private boolean isOriginalStyle = false; // 是否为原始样式

    // 预定义颜色数组
    private final int[] predefinedColors = {
            Color.rgb(255, 255, 255), // 白色
            Color.rgb(67, 142, 219),  // 蓝色
            Color.rgb(255, 0, 0),     // 红色
            Color.rgb(128, 128, 128), // 灰色
            Color.rgb(135, 206, 235), // 浅蓝色
            Color.rgb(30, 58, 138),   // 深蓝色
            Color.rgb(255, 192, 203), // 粉色
            Color.rgb(50, 205, 50),   // 绿色
            -1, // 自定义颜色标识
            -2  // 原始样式标识
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 截屏禁止功能已在Application中统一处理，此处移除重复代码
        // 状态栏颜色已在Application中统一处理，此处移除重复代码
        setContentView(R.layout.activity_picture_preview);
        cropImageView = findViewById(R.id.cropImageView);
        customColorView = findViewById(R.id.view_custom_color);
        initView();
        resultPath = getIntent().getStringExtra(PICK_IMG);
        generateIdPhotoOnly();
        AdSdk.getInstance().showInterAd(this);
    }

    /**
     * 仅生成证件照（带完整fallback策略）
     */
    private void generateIdPhotoOnly() {
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");

        // 使用带fallback的证件照生成
        IdPhotoManager.getInstance().generateIdPhotoWithFallback(this, resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (tipsDialog == null) {
                    tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                            .setIconType(ICON_TYPE_LOADING)
                            .setTipWord("正在处理请耐心等待...")
                            .create();
                }
                tipsDialog.show();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {

                originBitmap = bitmap;
                cropImageView.setImageBitmap(originBitmap);
            }

            @Override
            public void onError(String error) {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();

                }
                ToastUtils.showShort("所有处理方案都失败了: " + error);
            }

            @Override
            public void onComplete() {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                // 处理完成
            }
        });
    }

    /**
     * 获取原始样式照片
     */
    private void getOriginalStylePhoto() {
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");

        // 使用仅裁剪的API获取照片（不进行背景处理）
        IdPhotoManager.getInstance().generateCroppedIdPhoto(resultPath, picSize, new ApiCallback<Bitmap>() {
            @Override
            public void onStart() {
                if (tipsDialog == null) {
                    tipsDialog = new QMUITipDialog.Builder(PicturePreviewActivity.this)
                            .setIconType(ICON_TYPE_LOADING)
                            .setTipWord("正在处理请耐心等待...")
                            .create();
                }
                tipsDialog.show();
            }

            @Override
            public void onSuccess(Bitmap bitmap) {
                originBitmap = bitmap;
                drawImage(9, false); // 显示为原始样式
            }

            @Override
            public void onError(String error) {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                ToastUtils.showShort("获取原始样式失败: " + error);
                // 失败时回退到普通样式
                changePos(0);
            }

            @Override
            public void onComplete() {
                if (tipsDialog != null) {
                    tipsDialog.dismiss();
                }
                // 处理完成
            }
        });
    }

    public void drawImage(int colorPos, boolean isNext) {
        if (originBitmap == null) {
            ToastUtils.showShort("数据解析失败");
            return;
        }

        int selectColor;
        if (colorPos < predefinedColors.length - 2) {
            // 使用预定义颜色
            selectColor = predefinedColors[colorPos];
            isOriginalStyle = false;
        } else if (colorPos == predefinedColors.length - 2) {
            // 使用自定义颜色
            selectColor = customColor;
            isOriginalStyle = false;
        } else {
            // 原始样式
            isOriginalStyle = true;
            selectColor = -1; // 不使用
        }

        curPos = colorPos;

        // 生成预览图片
        Bitmap canvasBitmap;
        // 原始样式 - 直接使用原图
        if (isOriginalStyle) {
            canvasBitmap = originBitmap.copy(Bitmap.Config.ARGB_8888, false);
        } else {
            // 带背景颜色的样式
            int bH = originBitmap.getHeight();
            int bW = originBitmap.getWidth();
            canvasBitmap = Bitmap.createBitmap(bW, bH, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(canvasBitmap);
            Paint paint = new Paint();
            paint.setColor(selectColor);
            paint.setStyle(Paint.Style.FILL);
            canvas.drawRect(0, 0, bW, bH, paint);
            canvas.drawBitmap(originBitmap, new Rect(0, 0, bW, bH), new Rect(0, 0, bW, bH), paint);
        }

        // 显示预览图片
        cropImageView.setImageBitmap(canvasBitmap);

        // 只有在点击"下一步"时才保存为最终图片
        if (isNext) {
            destBitmap = canvasBitmap;
        }
    }

    private void initView() {
        QMUITopBar topBar = findViewById(R.id.top_bar);
        topBar.setTitle("调整背景颜色");
        topBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });

        // 设置点击监听器
        findViewById(R.id.btn_next).setOnClickListener(this);
        findViewById(R.id.ll_white).setOnClickListener(this);
        findViewById(R.id.ll_blue).setOnClickListener(this);
        findViewById(R.id.ll_red).setOnClickListener(this);
        findViewById(R.id.ll_gray).setOnClickListener(this);
        findViewById(R.id.ll_light_blue).setOnClickListener(this);
        findViewById(R.id.ll_dark_blue).setOnClickListener(this);
        findViewById(R.id.ll_pink).setOnClickListener(this);
        findViewById(R.id.ll_green).setOnClickListener(this);
        findViewById(R.id.ll_custom).setOnClickListener(this);
        findViewById(R.id.ll_original).setOnClickListener(this);

        // 更新自定义颜色显示
        updateCustomColorView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        disposables.dispose();
        if (destBitmap != null && !destBitmap.isRecycled()) {
            destBitmap.recycle();
        }
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btn_next) {
            getNext();
        } else if (id == R.id.ll_custom) {
            showColorPicker();
        } else if (id == R.id.ll_original) {
            getOriginalStylePhoto();
        } else {
            for (int i = 0; i < ids.length; i++) {
                if (id == ids[i]) {
                    changePos(i);
                    break;
                }
            }
        }
    }

    private void getNext() {
        drawImage(curPos, true);
        String filePath = ImageUtils.save2Album(destBitmap == null ? BitmapFactory.decodeFile(resultPath) : destBitmap, Bitmap.CompressFormat.JPEG).getPath();
        PicSize picSize = (PicSize) getIntent().getSerializableExtra("SIZE");
        Intent intent = new Intent(PicturePreviewActivity.this, SavePicActivity.class);
        intent.putExtra(PICK_IMG, filePath);
        intent.putExtra("SIZE", picSize);
        intent.putExtra("IS_ORIGINAL_STYLE", isOriginalStyle);
        startActivity(intent);
    }

    /**
     * 切换选中的背景颜色位置
     */
    private void changePos(int pos) {
        // 更新当前位置
        curPos = pos;
        
        // 更新选中指示器的可见性
        for (int i = 0; i < ids.length; i++) {
            findViewById(ids[i]).setVisibility(pos == i ? View.VISIBLE : View.INVISIBLE);
        }
        
        if (pos == 9) {
            // 原始样式选中时，不需要重新绘制图片，因为已经在getOriginalStylePhoto中处理
            if (!isOriginalStyle) {
                getOriginalStylePhoto();
            }
        } else {
            // 其他颜色选中时，如果之前是原始样式，需要重新生成带背景的照片
            if (isOriginalStyle) {
                // 重新生成普通证件照
                generateIdPhotoOnly();
            }
            drawImage(pos, false);
        }
    }

    /**
     * 显示颜色选择器对话框
     */
    private void showColorPicker() {
        // 保存当前颜色，以便取消时恢复
        final int originalColor = customColor;
        final int originalPos = curPos;

        ColorPickerDialog dialog = new ColorPickerDialog(this);
        dialog.setColor(customColor);

        // 设置实时颜色变化监听器
        dialog.setOnColorChangedListener(new ColorPickerDialog.OnColorChangedListener() {
            @Override
            public void onColorChanged(int color) {
                // 实时更新自定义颜色并预览
                customColor = color;
                updateCustomColorView();
                // 实时预览颜色效果
                drawImage(8, false); // 自定义颜色位置，不保存为最终图片
            }
        });

        // 设置最终确认监听器
        dialog.setOnColorSelectedListener(new ColorPickerDialog.OnColorSelectedListener() {
            @Override
            public void onColorSelected(int color) {
                customColor = color;
                updateCustomColorView();
                changePos(8); // 自定义颜色位置，更新选中状态
            }
        });

        // 设置取消监听器，恢复原来的颜色
        dialog.setOnCancelListener(dialogInterface -> {
            customColor = originalColor;
            updateCustomColorView();
            // 恢复原来的预览
            drawImage(originalPos, false);
        });

        dialog.show();
    }

    /**
     * 更新自定义颜色显示
     */
    private void updateCustomColorView() {
        if (customColorView != null) {
            GradientDrawable drawable = new GradientDrawable();
            drawable.setShape(GradientDrawable.RECTANGLE);
            drawable.setColor(customColor);
            drawable.setCornerRadius(40f);
            drawable.setStroke(4, Color.parseColor("#666666"));
            customColorView.setBackground(drawable);
        }
    }

}
