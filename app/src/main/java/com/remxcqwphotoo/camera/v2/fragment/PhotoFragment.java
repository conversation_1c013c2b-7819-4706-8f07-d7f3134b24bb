package com.remxcqwphotoo.camera.v2.fragment;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.core.content.FileProvider;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ToastUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.remxcqwphotoo.camera.R;

import com.remxcqwphotoo.camera.Const;
import com.remxcqwphotoo.camera.PicturePreviewActivity;
import com.remxcqwphotoo.camera.ad.AdSdk;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.mgr.FaceDetectManager;
import com.remxcqwphotoo.camera.utils.GlideEngine;
import com.remxcqwphotoo.camera.dialog.CustomSizeDialog;
import com.remxcqwphotoo.camera.v2.GuideActivity;
import com.remxcqwphotoo.camera.v2.SpecListActivity;
import com.remxcqwphotoo.camera.v2.adapter.PopularSpecAdapter;
import com.remxcqwphotoo.camera.v2.base.BaseFragment;
import com.remxcqwphotoo.camera.v2.fragment.fav.SizeListFragment;
import com.remxcqwphotoo.camera.v2.fragment.fav.TakePhotoSkillsFragment;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;

import com.qmuiteam.qmui.widget.dialog.QMUIBottomSheet;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.ButterKnife;

public class PhotoFragment extends BaseFragment {
    private PicSize selectSize = null;
    private RecyclerView rvPopularSpecs;
    private PopularSpecAdapter popularSpecAdapter;

    // Permission Request Codes (Fragment specific)
    private static final int REQUEST_CAMERA_PERMISSION_FRAGMENT = 201;
    private static final int REQUEST_STORAGE_PERMISSION_FRAGMENT = 202;

    // 系统相机相关常量
    private static final int REQUEST_SYSTEM_CAMERA = 203;
    private Uri currentPhotoUri;


    @Override
    protected View onCreateView() {
        FrameLayout layout = (FrameLayout) LayoutInflater.from(getActivity()).inflate(R.layout.fragment_photo, null);

        ButterKnife.bind(this, layout);
        LinearLayout content = layout.findViewById(R.id.content);
        layout.findViewById(R.id.top1).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startFragment(SizeListFragment.newInstance(SizeListFragment.SIGN));
            }
        });
        layout.findViewById(R.id.top2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), SpecListActivity.class);
                intent.putExtra(SpecListActivity.ENTER_TYPE, 0);
                startActivity(intent);
            }
        });
        FrameLayout adContainer = layout.findViewById(R.id.ad_container);
        AdSdk.getInstance().showBanner(getActivity(), adContainer);
        layout.findViewById(R.id.top3).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startFragment(new TakePhotoSkillsFragment());

            }
        });
        layout.findViewById(R.id.section_view).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), SpecListActivity.class);
                startActivity(intent);
            }
        });
        // Initialize Popular Specifications RecyclerView
        initPopularSpecsRecyclerView(layout);

        // Custom Size Button Click Listener
        layout.findViewById(R.id.btn_custom_size).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showCustomSizeDialog();
            }
        });

        layout.findViewById(R.id.banner).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), GuideActivity.class);
                startActivity(intent);
            }
        });
        return layout;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }


    private void showChoiceDialog() {
        QMUIBottomSheet bottomSheet = new QMUIBottomSheet(getContext());
        bottomSheet.setContentView(R.layout.custom_dialog_layout);
        bottomSheet.show();
        bottomSheet.findViewById(R.id.close_indicator).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheet.dismiss();
            }
        });
        bottomSheet.findViewById(R.id.btn_photo).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAlbum();
            }
        });
        bottomSheet.findViewById(R.id.btn_camera).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openCamera();
            }
        });
    }

    public void openPreview(String path) {
        Intent intent = new Intent(getActivity(), PicturePreviewActivity.class);
        intent.putExtra(PicturePreviewActivity.PICK_IMG, path);
        intent.putExtra("SIZE", selectSize);
        startActivity(intent);
    }

    private void openCamera() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startCameraActivity();
        } else {
            new QMUIDialog.MessageDialogBuilder(getActivity()).setMessage("此功能需要相机权限拍照,是否允许获取相机权限?").addAction(new QMUIDialogAction(getActivity(), "取消", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                }
            })).addAction(new QMUIDialogAction(getActivity(), "确定", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                    checkAndRequestCameraPermission();
                }
            })).create().show();
        }

    }

    private void checkAndRequestCameraPermission() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startCameraActivity();
        } else {
            // Consider showing a rationale before requesting? (Optional)
            // shouldShowRequestPermissionRationale(...)
            requestPermissions(new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION_FRAGMENT);
        }
    }

    private void startCameraActivity() {

        startSystemCamera();
    }

    /**
     * 启动系统相机
     */
    private void startSystemCamera() {
        PictureSelector.create(getContext()).openCamera(SelectMimeType.ofImage()).forResult(
                new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String picPath = result.get(0).getRealPath();
                        new FaceDetectManager(getActivity(), picPath).startWithFallback(new FaceDetectManager.DetectBack() {
                            @Override
                            public void onSuccess(String path) {
                                openPreview(path);
                            }

                            @Override
                            public void onFail(String msg) {
                                if (!TextUtils.isEmpty(msg)) {
                                    ToastUtil.showToast(getActivity(), msg);
                                }
                            }
                        });
                    }

                    @Override
                    public void onCancel() {
                    }
                }
        );
    }

    /**
     * 创建图片文件
     */
    private File createImageFile() throws IOException {
        // 创建图片文件名
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = requireActivity().getExternalFilesDir(Environment.DIRECTORY_PICTURES);

        File image = File.createTempFile(imageFileName,  /* prefix */
                ".jpg",         /* suffix */
                storageDir      /* directory */);

        return image;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_SYSTEM_CAMERA && resultCode == Activity.RESULT_OK) {
            // 系统相机拍照成功
            if (currentPhotoUri != null) {
                // 获取实际的文件路径
                String photoPath = getRealPathFromUri(currentPhotoUri);
                if (photoPath != null) {
                    // 进行人脸检测
                    performFaceDetection(photoPath);
                } else {
                    ToastUtil.showToast(getActivity(), "获取图片路径失败");
                }
            }
        }
    }

    /**
     * 从Uri获取真实的文件路径
     */
    private String getRealPathFromUri(Uri uri) {
        try {
            // 由于我们使用FileProvider创建的Uri，需要从Uri中解析出实际文件路径
            String uriString = uri.toString();
            if (uriString.contains("external_files")) {
                // 从Uri中提取文件名
                String fileName = uriString.substring(uriString.lastIndexOf("/") + 1);
                File storageDir = requireActivity().getExternalFilesDir(Environment.DIRECTORY_PICTURES);
                File actualFile = new File(storageDir, fileName);

                // 验证文件是否存在
                if (actualFile.exists()) {
                    return actualFile.getAbsolutePath();
                }
            }

            // 如果上述方法失败，尝试直接使用Uri的路径
            String path = uri.getPath();
            if (path != null) {
                File file = new File(path);
                if (file.exists()) {
                    return path;
                }
            }

            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 执行人脸检测
     */
    private void performFaceDetection(String imagePath) {
        new FaceDetectManager(getActivity(), imagePath).startWithFallback(new FaceDetectManager.DetectBack() {
            @Override
            public void onSuccess(String path) {
                openPreview(path);
            }

            @Override
            public void onFail(String msg) {
                if (!TextUtils.isEmpty(msg)) {
                    ToastUtil.showToast(getActivity(), msg);
                }
            }
        });
    }


    private void openAlbum() {
        String permissionNeeded = getStoragePermission();

        if (ContextCompat.checkSelfPermission(requireContext(), permissionNeeded) == PackageManager.PERMISSION_GRANTED) {
            openGallery();
        } else {
            String permsTips = "";
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                permsTips = "选择图片需要获取您存储权限是否允许？";
            } else {
                permsTips = "选择图片需要获取您的读取相册权限是否允许？";
            }
            new QMUIDialog.MessageDialogBuilder(getActivity()).setMessage(permsTips).addAction(new QMUIDialogAction(getActivity(), "不允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                }
            })).addAction(new QMUIDialogAction(getActivity(), "允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                    requestStoragePermission();
                }
            })).create().show();
        }
    }

    private String getStoragePermission() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ? Manifest.permission.READ_MEDIA_IMAGES : Manifest.permission.READ_EXTERNAL_STORAGE;
    }

    private void requestStoragePermission() {
        String permissionNeeded = getStoragePermission();
        // Consider showing a rationale before requesting? (Optional)
        // shouldShowRequestPermissionRationale(...)
        requestPermissions(new String[]{permissionNeeded}, REQUEST_STORAGE_PERMISSION_FRAGMENT);
    }


    private void openGallery() {
        PictureSelector.create(this).openGallery(SelectMimeType.ofImage()).isDisplayCamera(false).setMaxSelectNum(1).setImageEngine(GlideEngine.Companion.createGlideEngine()).forResult(new OnResultCallbackListener<LocalMedia>() {

            @Override
            public void onResult(ArrayList<LocalMedia> result) {
                String picPath = result.get(0).getRealPath();
                new FaceDetectManager(getActivity(), picPath).startWithFallback(new FaceDetectManager.DetectBack() {
                    @Override
                    public void onSuccess(String path) {
                        openPreview(path);
                    }

                    @Override
                    public void onFail(String msg) {
                        if (!TextUtils.isEmpty(msg)) {
                            ToastUtil.showToast(getActivity(), msg);
                        }
                    }
                });
            }

            @Override
            public void onCancel() {
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQUEST_CAMERA_PERMISSION_FRAGMENT:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startCameraActivity();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请打开相机权限");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
            case REQUEST_STORAGE_PERMISSION_FRAGMENT:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openGallery();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请允许获取相关授权");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
        }
    }

    /**
     * Initialize Popular Specifications RecyclerView
     */
    private void initPopularSpecsRecyclerView(FrameLayout layout) {
        rvPopularSpecs = layout.findViewById(R.id.rv_popular_specs);

        // Get first 12 items from allPicList
        List<PicSize> popularSpecs = new ArrayList<>();
        int maxItems = Math.min(12, Const.allPicList.size());
        for (int i = 0; i < maxItems; i++) {
            popularSpecs.add(Const.allPicList.get(i));
        }

        // Set up GridLayoutManager with 3 columns
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 3);
        rvPopularSpecs.setLayoutManager(gridLayoutManager);

        // Initialize adapter
        popularSpecAdapter = new PopularSpecAdapter(getContext(), popularSpecs);
        popularSpecAdapter.setOnSpecClickListener(new PopularSpecAdapter.OnSpecClickListener() {
            @Override
            public void onSpecClick(PicSize picSize) {
                selectSize = picSize;
                showChoiceDialog();
            }
        });

        rvPopularSpecs.setAdapter(popularSpecAdapter);
    }

    /**
     * Show Custom Size Dialog
     */
    private void showCustomSizeDialog() {
        CustomSizeDialog customSizeDialog = new CustomSizeDialog(getActivity());
        customSizeDialog.setOnCustomSizeListener(new CustomSizeDialog.OnCustomSizeListener() {
            @Override
            public void onCustomSizeConfirmed(PicSize customSize) {
                selectSize = customSize;
                showChoiceDialog();
            }

            @Override
            public void onCustomSizeCancelled() {
                // User cancelled, do nothing
            }
        });
        customSizeDialog.show();
    }
}
