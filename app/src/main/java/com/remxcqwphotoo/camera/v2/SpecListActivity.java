package com.remxcqwphotoo.camera.v2;

import android.Manifest;
import android.app.StatusBarManager;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ToastUtils;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.remxcqwphotoo.camera.Const;
import com.remxcqwphotoo.camera.PicturePreviewActivity;
import com.remxcqwphotoo.camera.R;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.mgr.FaceDetectManager;
import com.remxcqwphotoo.camera.utils.GlideEngine;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;
import com.remxcqwphotoo.camera.view.HomeItemView;
import com.qmuiteam.qmui.widget.QMUITopBar;

import java.util.ArrayList;
import java.util.List;

public class SpecListActivity extends AppCompatActivity {
    private QMUITopBar topBar;
    private RecyclerView rvList;
    // Permission Request Codes
    private static final int REQUEST_CAMERA_PERMISSION = 101;
    private static final int REQUEST_STORAGE_PERMISSION = 102;

    private PicSize selectSize;
    public static final String ENTER_TYPE = "ENTER_TYPE";
    private int enterType = -1;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_spec);
        initTopBar();
        initList();
        enterType = getIntent().getIntExtra(ENTER_TYPE, -1);
    }

    public int getEnterType() {
        return enterType;
    }

    private void initList() {
        rvList = findViewById(R.id.rv_list);
        rvList.setLayoutManager(new GridLayoutManager(this, 2));
        rvList.setAdapter(new SpecAdapter());
    }

    private void initTopBar() {
        topBar = findViewById(R.id.top_bar);
        topBar.addLeftBackImageButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        topBar.setTitle("所有规格（基于300dpi）");
    }

    public void openPreview(String path) {
        Intent intent = new Intent(this, PicturePreviewActivity.class);
        intent.putExtra(PicturePreviewActivity.PICK_IMG, path);
        intent.putExtra("SIZE", this.selectSize);
        startActivity(intent);
    }

    private void openCamera(PicSize picSize) {
        this.selectSize = picSize;
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startCameraActivity();
        } else {
            new QMUIDialog.MessageDialogBuilder(this).setMessage("此功能需要相机权限拍照,是否允许获取相机权限?")
                    .addAction(new QMUIDialogAction(this, "取消", new QMUIDialogAction.ActionListener() {
                        @Override
                        public void onClick(QMUIDialog dialog, int index) {
                            dialog.dismiss();
                        }
                    }))
                    .addAction(new QMUIDialogAction(this, "确定", new QMUIDialogAction.ActionListener() {
                        @Override
                        public void onClick(QMUIDialog dialog, int index) {
                            dialog.dismiss();
                            checkAndRequestCameraPermission();
                        }
                    })).create().show();
        }
    }

    private void checkAndRequestCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            startCameraActivity();
        } else {
            // Consider showing a rationale before requesting? (Optional)
            // ActivityCompat.shouldShowRequestPermissionRationale(...)
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, REQUEST_CAMERA_PERMISSION);
        }
    }

    private void startCameraActivity() {
        PictureSelector.create(this).openCamera(SelectMimeType.ofImage())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String picPath = result.get(0).getRealPath();
                        new FaceDetectManager(SpecListActivity.this, picPath).startWithFallback(new FaceDetectManager.DetectBack() {
                            @Override
                            public void onSuccess(String path) {
                                openPreview(path);
                            }

                            @Override
                            public void onFail(String msg) {
                                if (!TextUtils.isEmpty(msg)) {
                                    ToastUtil.showToast(SpecListActivity.this, msg);
                                }
                            }
                        });
                    }

                    @Override
                    public void onCancel() {
                    }
                });
    }

    private void openAlbum(PicSize picSize) {
        this.selectSize = picSize;
        chooseImage();
    }

    private void chooseImage() {
        String permissionNeeded = getStoragePermission();

        if (ContextCompat.checkSelfPermission(this, permissionNeeded) == PackageManager.PERMISSION_GRANTED) {
            openGallery();
        } else {
            String permsTips = "";
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) { // Android 13
                permsTips = "选择图片需要获取您存储权限是否允许？";
            } else {
                permsTips = "选择图片需要获取您的读取相册权限是否允许？";
            }
            new QMUIDialog.MessageDialogBuilder(this).setMessage(permsTips).addAction(new QMUIDialogAction(this, "不允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                }
            })).addAction(new QMUIDialogAction(this, "允许", new QMUIDialogAction.ActionListener() {
                @Override
                public void onClick(QMUIDialog dialog, int index) {
                    dialog.dismiss();
                    requestStoragePermission();
                }
            })).create().show();
        }
    }

    private String getStoragePermission() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ?
                Manifest.permission.READ_MEDIA_IMAGES : Manifest.permission.READ_EXTERNAL_STORAGE;
    }

    private void requestStoragePermission() {
        String permissionNeeded = getStoragePermission();
        // Consider showing a rationale before requesting? (Optional)
        // ActivityCompat.shouldShowRequestPermissionRationale(...)
        ActivityCompat.requestPermissions(this, new String[]{permissionNeeded}, REQUEST_STORAGE_PERMISSION);
    }

    private void openGallery() {
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setMaxSelectNum(1).isDisplayCamera(false)
                .setImageEngine(GlideEngine.Companion.createGlideEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {

                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        String picPath = result.get(0).getRealPath();
                        new FaceDetectManager(SpecListActivity.this, picPath).startWithFallback(new FaceDetectManager.DetectBack() {
                            @Override
                            public void onSuccess(String path) {
                                openPreview(path);
                            }

                            @Override
                            public void onFail(String msg) {
                                if (!TextUtils.isEmpty(msg)) {
                                    ToastUtil.showToast(SpecListActivity.this, msg);
                                }
                            }
                        });
                    }

                    @Override
                    public void onCancel() {
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQUEST_CAMERA_PERMISSION:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startCameraActivity();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请打开相机权限");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
            case REQUEST_STORAGE_PERMISSION:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openGallery();
                } else {
                    // Permission Denied
                    ToastUtils.showLong("请允许获取相关授权");
                    // Optionally, guide user to settings
                    // AppUtils.launchAppDetailsSettings();
                }
                break;
        }
    }

    public static class SpecAdapter extends RecyclerView.Adapter<SpecAdapter.SpecHolder> {
        private List<PicSize> sizes = Const.allPicList;

        @NonNull
        @Override
        public SpecHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new SpecHolder(new HomeItemView(parent.getContext()));
        }

        @Override
        public void onBindViewHolder(@NonNull SpecHolder holder, int position) {
            holder.initView(sizes.get(position));
        }

        @Override
        public int getItemCount() {
            return sizes.size();
        }

        public static class SpecHolder extends RecyclerView.ViewHolder {
            private HomeItemView itemView;

            public SpecHolder(@NonNull View itemView) {
                super(itemView);
                this.itemView = (HomeItemView) itemView;
            }

            public void initView(PicSize picSize) {
                itemView.setItemData(picSize, (dialog, which) -> {
                    if (dialog != null) {
                        dialog.dismiss();
                    }
                    SpecListActivity act = (SpecListActivity) itemView.getContext();
                    act.openAlbum(picSize);
                }, (dialog, which) -> {
                    if (dialog != null) {
                        dialog.dismiss();
                    }
                    SpecListActivity act = (SpecListActivity) itemView.getContext();
                    act.openCamera(picSize);
                });
            }
        }
    }
}
