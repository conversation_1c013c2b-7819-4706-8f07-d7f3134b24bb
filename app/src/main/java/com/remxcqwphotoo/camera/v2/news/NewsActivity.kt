package com.remxcqwphotoo.camera.v2.news

import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.just.agentweb.AgentWeb
import com.just.agentweb.WebChromeClient
import com.qmuiteam.qmui.util.QMUIStatusBarHelper
import com.qmuiteam.qmui.widget.QMUITopBarLayout
import com.remxcqwphotoo.camera.R
import kotlinx.android.synthetic.main.activity_news_new.news_new_container
import kotlinx.android.synthetic.main.activity_news_new.news_new_toolbar


class NewsActivity : AppCompatActivity() {

    companion object {
        const val NEWS_URL = "news_url"
        const val NEWS_TITLE = "news_title"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 截屏禁止功能已在Application中统一处理，此处移除重复代码
        // 状态栏颜色已在Application中统一处理，此处移除重复代码
        setContentView(R.layout.activity_news_new)

        val url: String? = intent.getStringExtra(NEWS_URL)

        news_new_toolbar.setTitleGravity(Gravity.CENTER_VERTICAL)
        news_new_toolbar.addLeftBackImageButton()
            .setOnClickListener {
                finish()
            }

        url?.let {
            val agentWeb = AgentWeb.with(this)
                .setAgentWebParent(news_new_container, LinearLayout.LayoutParams(-1, -1))
                .useDefaultIndicator()
                .setWebChromeClient(MyWebViewChromeClient(news_new_toolbar))
                .createAgentWeb()
                .ready()
                .go(it)

            // 禁用WebView暗黑模式

        }
    }
}

class MyWebViewChromeClient(val topBarLayout: QMUITopBarLayout) : WebChromeClient() {

    override fun onReceivedTitle(view: WebView?, title: String?) {
        super.onReceivedTitle(view, title)
        title?.let { topBarLayout.setTitle(it) }
    }
}