package com.remxcqwphotoo.camera;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapRegionDecoder;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


import com.blankj.utilcode.util.ImageUtils;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.service.ApiConfig;
import com.remxcqwphotoo.camera.service.mgr.IdPhotoManager;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.base.BaseFragment;
import com.qmuiteam.qmui.widget.QMUITabSegment;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;

public class SinglePicFragment extends BaseFragment {
    private ImageView cropImageView;
    private String filePath;
    private Bitmap originBitmap = null;
    private float originHeight;
    private float originWidth;
    private Bitmap destBitmap;
    private int saveType = -1;
    private PicSize picSize;
    private boolean isImageProcessing = false;
    private boolean isImageReady = false;
    private boolean isOriginalStyle = false; // 是否为原始样式

    // 新增相纸尺寸选择相关控件
    private QMUITabSegment paperSizeTabs;
    private Switch cropLineSwitch;
    private String currentPaperSize = ApiConfig.DEFAULT_PAPER_SIZE;
    private boolean withCropLine = false;

    public static SinglePicFragment newInstance(int type, String filePath, PicSize picSize) {
        Bundle arg = new Bundle();
        arg.putInt("POS", type);
        arg.putString(PicturePreviewActivity.PICK_IMG, filePath);
        arg.putSerializable("SIZE", picSize);
        SinglePicFragment singlePicFragment = new SinglePicFragment();
        singlePicFragment.setArguments(arg);
        return singlePicFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        filePath = getArguments().getString(PicturePreviewActivity.PICK_IMG);
        saveType = getArguments().getInt("POS");
        picSize = (PicSize) getArguments().getSerializable("SIZE");

        // 添加调试日志
        android.util.Log.d("SinglePicFragment", "Fragment创建 - saveType: " + saveType + ", filePath: " + filePath);
    }

    @Override
    protected View onCreateView() {
        return LayoutInflater.from(getContext()).inflate(R.layout.fragment_layout_single_pic, null, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        originBitmap = BitmapFactory.decodeFile(filePath);
        destBitmap = originBitmap;
        originWidth = originBitmap.getWidth();
        originHeight = originBitmap.getHeight();
        cropImageView = view.findViewById(R.id.crop_image);
        cropImageView.setImageBitmap(originBitmap);

        // 初始化相纸尺寸选择控件
        if (saveType == 2) {
            initPaperSizeSelector(view);
            generatePreviewWithApi();
        } else {
            // 隐藏相纸选择器（仅排版照需要）
            view.findViewById(R.id.paper_size_selector).setVisibility(View.GONE);
        }
    }

    /**
     * 初始化相纸尺寸选择器
     */
    private void initPaperSizeSelector(View view) {
        paperSizeTabs = view.findViewById(R.id.paper_size_tabs);
        cropLineSwitch = view.findViewById(R.id.crop_line_switch);

        // 设置相纸尺寸选项
        paperSizeTabs.addTab(new QMUITabSegment.Tab("6寸"));
        paperSizeTabs.addTab(new QMUITabSegment.Tab("5寸"));
        paperSizeTabs.addTab(new QMUITabSegment.Tab("A4"));
        paperSizeTabs.addTab(new QMUITabSegment.Tab("3R"));
        paperSizeTabs.addTab(new QMUITabSegment.Tab("4R"));

        // 设置默认选中6寸
        paperSizeTabs.selectTab(0);
        paperSizeTabs.setMode(QMUITabSegment.MODE_FIXED);

        // 设置Tab切换监听
        paperSizeTabs.addOnTabSelectedListener(new QMUITabSegment.OnTabSelectedListener() {
            @Override
            public void onTabSelected(int index) {
                switch (index) {
                    case 0:
                        currentPaperSize = ApiConfig.PAPER_SIZE_6_INCH;
                        break;
                    case 1:
                        currentPaperSize = ApiConfig.PAPER_SIZE_5_INCH;
                        break;
                    case 2:
                        currentPaperSize = ApiConfig.PAPER_SIZE_A4;
                        break;
                    case 3:
                        currentPaperSize = ApiConfig.PAPER_SIZE_3R;
                        break;
                    case 4:
                        currentPaperSize = ApiConfig.PAPER_SIZE_4R;
                        break;
                }
                // 重新生成预览
                generatePreviewWithApi();
            }

            @Override
            public void onTabUnselected(int index) {
                // 不需要处理
            }

            @Override
            public void onTabReselected(int index) {
                // 不需要处理
            }

            @Override
            public void onDoubleTap(int index) {
                // 不需要处理双击
            }
        });

        // 设置裁剪线开关监听
        cropLineSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                withCropLine = isChecked;
                generatePreviewWithApi();
            }
        });
    }

    @SuppressLint("CheckResult")
    private void generatePreviewWithApi() {
        if (saveType == 1) {
            // 单张照片，直接显示原图
            formatImage();
            return;
        }
        QMUITipDialog tipDialog = new QMUITipDialog.Builder(getContext()).setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).setTipWord("正在生成预览...").create();
        tipDialog.show();
        IdPhotoManager.getInstance().generateLayoutPhotoWithPaperSize(filePath, picSize, currentPaperSize, cropLineSwitch.isChecked(), new ApiCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap previewBitmap) {
                tipDialog.dismiss();
                cropImageView.setImageBitmap(previewBitmap);
                destBitmap = previewBitmap;
                // 如果是排版模式，旋转90度显示
                if (saveType == 2) {
                    cropImageView.setRotation(90);
                }
            }

            @Override
            public void onError(String error) {
                tipDialog.dismiss();
                formatImageFallback();
            }
        });
    }

    // 备用的本地处理方法
    private void formatImageFallback() {
        formatImage();
    }

    private void formatMultiImage() {
        int space = 20;
        int bH = destBitmap.getHeight();
        int bW = destBitmap.getWidth();
        int totalW = bW * 4 + space * 5;
        int totalH = bH * 2 + space * 3;
        Bitmap canvasBitmap = Bitmap.createBitmap(totalW, totalH, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(canvasBitmap);
        Paint paint = new Paint();
        paint.setColor(Color.parseColor("#ffffff"));
        paint.setStyle(Paint.Style.FILL);
        canvas.drawRect(0, 0, totalW, totalH, paint);
        for (int i = 0; i < 8; i++) {
            if (i < 4) {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i + 1) + bW * i, space, space * (i + 1) + bW * i + bW, bH + space), paint);
            } else {
                canvas.drawBitmap(destBitmap, new Rect(0, 0, bW, bH), new Rect(space * (i - 3) + bW * (i - 4), bH + 2 * space, space * (i - 3) + bW * (i - 4) + bW, 2 * bH + 2 * space), paint);
            }
        }
        cropImageView.setImageBitmap(canvasBitmap);
        destBitmap = canvasBitmap;
        cropImageView.setRotation(90);
    }

    private void formatImage() {
        try {
            float height = picSize.height;
            float width = picSize.width;
            float minScale = Math.min(originHeight / height, originWidth / width);
            if (minScale < 1) {
                Matrix matrix = new Matrix();
                matrix.postScale(1 / minScale, 1 / minScale);
                Bitmap newBitmap = Bitmap.createBitmap(originBitmap, 0, 0, originBitmap.getWidth(), originBitmap.getHeight(), matrix, true);
                String tempPath = ImageUtils.save2Album(newBitmap, Bitmap.CompressFormat.JPEG).getPath();
                originHeight = newBitmap.getHeight();
                originWidth = newBitmap.getWidth();
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(tempPath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
                cropImageView.setImageBitmap(destBitmap);
            } else {
                BitmapRegionDecoder decoder = BitmapRegionDecoder.newInstance(filePath, true);
                float scaleWidth = width * minScale;
                float scaleHeight = height * minScale;
                int offsetX = Math.round((originWidth - scaleWidth) / 2);
                int offsetY = Math.round((originHeight - scaleHeight) / 2);
                Rect cropRect = new Rect(0, 0, Math.round(scaleWidth), Math.round(scaleHeight));
                cropRect.offsetTo(offsetX, offsetY);
                destBitmap = decoder.decodeRegion(cropRect, new BitmapFactory.Options());
                Matrix matrix = new Matrix();
                matrix.postScale(width / destBitmap.getWidth(), height / destBitmap.getHeight());
                destBitmap = Bitmap.createBitmap(destBitmap, 0, 0, destBitmap.getWidth(), destBitmap.getHeight(), matrix, true);
                cropImageView.setImageBitmap(destBitmap);
                cropImageView.setRotation(0);
            }
            if (saveType == 2) {
                formatMultiImage();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取最终的bitmap（供保存使用）
     */
    public Bitmap getBitmap() {
        return destBitmap;
    }

    /**
     * 获取当前选择的相纸尺寸
     */
    public String getCurrentPaperSize() {
        return currentPaperSize;
    }

    /**
     * 获取是否包含裁剪线
     */
    public boolean isWithCropLine() {
        return withCropLine;
    }

    public void saveCurrentImage(SaveCallback callback) {
        android.util.Log.d("SinglePicFragment", "saveCurrentImage调用 - saveType: " + saveType + ", destBitmap: " + (destBitmap != null ? "存在" : "null") + ", originBitmap: " + (originBitmap != null ? "存在" : "null"));

        if (destBitmap == null) {
            // 如果destBitmap为null，尝试使用originBitmap作为备用
            if (originBitmap != null) {
                android.util.Log.d("SinglePicFragment", "destBitmap为null，使用originBitmap作为备用");
                destBitmap = originBitmap;
            } else {
                android.util.Log.e("SinglePicFragment", "destBitmap和originBitmap都为null");
                if (callback != null) {
                    callback.onError("没有可保存的图片，请重新加载");
                }
                return;
            }
        }
        File file = ImageUtils.save2Album(destBitmap, Bitmap.CompressFormat.JPEG);
        if (file != null && file.exists()) {
            if (callback != null) {
                callback.onSuccess(file.getAbsolutePath());
            }
        } else {
            callback.onError("照片保存失败！");
        }
    }


    /**
     * 保存回调接口
     */
    public interface SaveCallback {
        void onStart();

        void onSuccess(String filePath);

        void onError(String error);
    }
}
