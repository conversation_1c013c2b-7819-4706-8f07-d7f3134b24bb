package com.remxcqwphotoo.camera

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.webkit.*
import androidx.appcompat.app.AppCompatActivity
import com.bytedance.sdk.openadsdk.TTAdNative
import com.bytedance.sdk.openadsdk.TTNativeExpressAd
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient
import com.remxcqwphotoo.camera.ad.AdSdk
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import kotlinx.android.synthetic.main.act_web.*
import java.util.concurrent.TimeUnit

open class WebPage : AppCompatActivity(), TTAdNative.NativeExpressAdListener {
    private var title: String? = null
    private var link: String? = null
    private var isData: Boolean = false

    companion object {
        const val TITLE = "data:title"
        const val LINK = "data:link"
        const val IS_DATA = "data:is_data"

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 截屏禁止功能已在Application中统一处理
        setContentView(R.layout.act_web)
        WebView.setWebContentsDebuggingEnabled(true)
        title = intent.getStringExtra(TITLE);
        link = intent.getStringExtra(LINK);
        isData = intent.getBooleanExtra(IS_DATA, false)
        initTopBar()
        initView()
        AdSdk.getInstance().showInterAd(this)
    }

    private fun initTopBar() {
        close.setOnClickListener {
            finish()
        }

        back.setOnClickListener {
            if (web.canGoBack()) {
                web.goBack()
            } else {
                finish()
            }

        }
        page_title.text = title
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initView() {
        web.settings.javaScriptEnabled = true

        web.webViewClient = object : QMUIWebViewClient(true, true) {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                if (request?.url == null) return false

                if (request?.url.toString().startsWith("http://") || request.url.toString()
                        .startsWith("https://")
                ) {
                    return super.shouldOverrideUrlLoading(view, request)
                } else {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, request.url)
                        startActivity(intent)
                    } catch (e: Exception) {
//                        ToastUtils.s(this@WebPage, "手机没有安装印象笔记！")
                    }
                    return true
                }
            }

            // document.getElementsByClassName('button-groups')[0].style.display ='none'

            @SuppressLint("CheckResult")
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                val jsHack = "(function(){\n" +
                        "var zzbtns =document.getElementsByClassName('button-groups'); for(var i=0;i<zzbtns.length;i++){zzbtns[i].style.display='none'};\n" +
                        "var commetList =document.getElementById('rel_news');if(commetList){commetList.style.display='none'};\n" +
                        "var commetList =document.getElementById('QIHOO360COMMENT');if(commetList){commetList.style.display='none'};\n" +
                        "var commet =document.getElementById('QIHOO360COMMENTF');if(commet){commet.style.display='none'};\n" +
                        "var btns =document.getElementsByTagName('Button'); for(var i=0;i<btns.length;i++){btns[i].style.display='none'};\n" +
                        "var as =document.getElementsByTagName('a'); for(var i=0;i<as.length;i++){ if(as[i].href='https://www.yinxiang.com/download/') as[i].style.display='none'};\n" +
                        "var mheader=document.getElementsByTagName('header')[0];if(mheader){mheader.style.display='none'}\n" +
                        "var mfooter=document.getElementsByClassName('sc-jWBwVP eBgsec')[0];if(mfooter){mfooter.style.display='none'}\n" +
                        "})()";
                view?.loadUrl("javascript:$jsHack")
                Observable.timer(400, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        view?.loadUrl("javascript:$jsHack")
                    }
                Observable.timer(2000, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        loading_layout.visibility = View.INVISIBLE
                    }
                Observable.timer(1300, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        view?.loadUrl("javascript:$jsHack")
                    }
                Observable.timer(1400, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        view?.loadUrl("javascript:$jsHack")
                    }
                Observable.timer(1500, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        view?.loadUrl("javascript:$jsHack")
                    }
                Observable.timer(2000, TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread()).subscribe {
                        view?.loadUrl("javascript:$jsHack")
                    }
            }
        }
        web.setDownloadListener { p0, p1, p2, p3, p4 ->
            val intent = Intent(Intent.ACTION_VIEW);
            intent.addCategory(Intent.CATEGORY_BROWSABLE);
            intent.data = Uri.parse(p0)
            startActivity(intent)
        }
        web.webChromeClient = WebChromeClient()
        if (isData) {
            web.loadDataWithBaseURL(null, link!!, "text/html", "charset=UTF-8", null)
        } else {
            web.loadUrl(link!!)
        }
    }

    override fun onError(p0: Int, p1: String?) {
    }

    override fun onNativeExpressAdLoad(list: MutableList<TTNativeExpressAd>?) {
        if (list != null && list.size > 0) {
            val ttNativeExpressAd: TTNativeExpressAd = list[0]
            ttNativeExpressAd.setExpressInteractionListener(object :
                TTNativeExpressAd.AdInteractionListener {
                override fun onAdDismiss() {}
                override fun onAdClicked(view: View, i: Int) {}
                override fun onAdShow(view: View, i: Int) {}
                override fun onRenderFail(view: View, s: String, i: Int) {
                }

                override fun onRenderSuccess(view: View, v: Float, v1: Float) {
                    ttNativeExpressAd.showInteractionExpressAd(this@WebPage)
                }
            })
            ttNativeExpressAd.render()
        }
    }
}