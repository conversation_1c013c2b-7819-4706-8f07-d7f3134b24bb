package com.remxcqwphotoo.camera.utils;

import android.app.Activity;
import android.graphics.Color;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

public class StatusBarUtils {

    public static void setStatusBarColor(int color, Activity activity) {
        Window window = activity.getWindow();
        // 清除 FLAG_TRANSLUCENT_STATUS 标记，以便 setStatusBarColor 生效
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        // 添加 FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS 标记
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        // 设置状态栏颜色
        window.setStatusBarColor(color);
        // Android 6.0 (API 23) 及以上版本可以动态修改状态栏图标和文字的颜色
        // 判断背景颜色是深色还是浅色
        // 如果颜色亮度 > 0.5，则为浅色，需要设置深色图标
        if (isColorLight(color)) {
            // 设置状态栏图标和文字为深色
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        } else {
            // 恢复默认的浅色图标和文字
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
        }
    }

    /**
     * 判断颜色是否为浅色
     *
     * @param color 颜色值
     * @return 如果是浅色则返回 true
     */
    private static boolean isColorLight(int color) {
        double darkness = 1 - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255;
        // 如果亮度小于 0.5，则认为是深色；否则为浅色
        return darkness < 0.5;
    }

}
