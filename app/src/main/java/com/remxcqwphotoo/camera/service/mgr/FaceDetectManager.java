package com.remxcqwphotoo.camera.service.mgr;

import android.app.Activity;
import android.text.TextUtils;

import com.blankj.utilcode.util.ToastUtils;
import com.remxcqwphotoo.camera.model.NewFaceDetectData;
import com.remxcqwphotoo.camera.service.ApiConfig;
import com.remxcqwphotoo.camera.service.FaceDetectionService;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;
import com.remxcqwphotoo.camera.v2.util.ToastUtil;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialogAction;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;

import java.io.File;

import top.zibin.luban.Luban;
import top.zibin.luban.OnCompressListener;

public class FaceDetectManager {
    private final String filePath;
    private Activity context;
    QMUITipDialog tipDialog;
    private DetectBack detectBack;

    public interface DetectBack {
        public void onSuccess(String path);

        public void onFail(String msg);
    }

    public FaceDetectManager(Activity context, String filePath) {
        this.context = context;
        this.filePath = filePath;
    }

    /**
     * 使用新的Service架构进行人脸检测（带fallback）
     */
    public void startWithFallback(DetectBack detectBack) {
        this.detectBack = detectBack;
        if (TextUtils.isEmpty(filePath)) {
            if (detectBack != null) {
                detectBack.onFail("图片路径为空");
            }
            return;
        }
        Luban.with(context).load(filePath).ignoreBy(100) // 小于100KB的图片不压缩
                .setCompressListener(new OnCompressListener() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onSuccess(File file) {
                        doDetect(file);
                    }

                    @Override
                    public void onError(Throwable e) {
                        doDetect(new File(filePath));
                        // 直接使用原图进行检测
                    }
                }).launch();
    }


    private void doDetect(File file) {
        // 使用新的人脸检测API，默认使用MTCNN模型
        FaceDetectionService.getInstance().detectFaceWithNewApi(file.getAbsolutePath(), ApiConfig.DEFAULT_FACE_MODEL, new ApiCallback<NewFaceDetectData>() {
            @Override
            public void onStart() {
                QMUITipDialog.Builder builder = new QMUITipDialog.Builder(context);
                tipDialog = builder.setTipWord("人脸检测中...").setIconType(QMUITipDialog.Builder.ICON_TYPE_LOADING).create();
                tipDialog.show();
            }

            @Override
            public void onSuccess(NewFaceDetectData faceData) {
                if (tipDialog != null) {
                    tipDialog.dismiss();
                }
                String actualImagePath = faceData.image_path != null ? faceData.image_path : filePath;
                handleFaceDetectResult(faceData, actualImagePath);
            }

            @Override
            public void onError(String error) {
                if (tipDialog != null) {
                    tipDialog.dismiss();
                }
                if (detectBack != null) {
                    detectBack.onFail(error);
                }
            }
        });


    }

    // 使用新的Service进行人脸检测（带完整fallback策略）

    /**
     * 处理新API的人脸检测结果
     */
    private void handleFaceDetectResult(NewFaceDetectData data, String sourcePath) {
        if (data.isValid()) {
            // 获取第一个人脸位置信息
            NewFaceDetectData.Rectangle faceRectangle = data.getFirstFaceRectangle();
            // 验证人脸区域是否有效
            if (faceRectangle == null || !faceRectangle.isValid()) {
                ToastUtil.showToast(context, "人脸区域无效");
                if (detectBack != null) {
                    detectBack.onFail("人脸区域无效");
                }
                return;
            }

            // 基于人脸区域进行基本的质量检查
            String msg = validateFaceQuality(faceRectangle);

            if (TextUtils.isEmpty(msg)) {
                // 检测通过
                if (detectBack != null) {
                    detectBack.onSuccess(sourcePath);
                }
            } else {
                // 显示质量问题对话框
                QMUIDialog msgDialog = new QMUIDialog.MessageDialogBuilder(context).addAction("是", new QMUIDialogAction.ActionListener() {
                    @Override
                    public void onClick(QMUIDialog dialog, int index) {
                        dialog.dismiss();
                        if (detectBack != null) {
                            detectBack.onFail("");
                        }
                    }
                }).addAction("否", new QMUIDialogAction.ActionListener() {
                    @Override
                    public void onClick(QMUIDialog dialog, int index) {
                        dialog.dismiss();
                        if (detectBack != null) {
                            detectBack.onSuccess(sourcePath);
                        }
                    }
                }).setMessage(msg + "是否继续重新操作?").create();
                msgDialog.show();
            }
        } else {
            ToastUtil.showToast(context, "没有检测到人脸");
            if (detectBack != null) {
                detectBack.onFail("没有检测到人脸");
            }
        }
    }

    /**
     * 基于人脸位置信息进行质量验证
     * 使用新API的Rectangle数据进行基本的尺寸和位置检查
     */
    private String validateFaceQuality(NewFaceDetectData.Rectangle faceRectangle) {
        // 检查人脸区域大小是否合适
        int faceWidth = faceRectangle.width;
        int faceHeight = faceRectangle.height;

        // 人脸区域太小
        if (faceWidth < 100 || faceHeight < 100) {
            return "人脸区域过小，建议靠近拍摄";
        }

        // 检查人脸宽高比是否合理（正常人脸宽高比约为0.7-0.9）
        double aspectRatio = (double) faceWidth / faceHeight;
        if (aspectRatio < 0.5 || aspectRatio > 1.2) {
            return "人脸比例异常，请调整拍摄角度";
        }

        // 检查人脸是否过于靠近边缘
        if (faceRectangle.left < 50 || faceRectangle.top < 50) {
            return "人脸过于靠近边缘，请调整拍摄位置";
        }

        return ""; // 验证通过
    }
}
