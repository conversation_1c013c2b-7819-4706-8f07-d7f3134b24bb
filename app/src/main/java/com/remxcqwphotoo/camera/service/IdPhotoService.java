package com.remxcqwphotoo.camera.service;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.remxcqwphotoo.camera.model.IdPhotoData;
import com.remxcqwphotoo.camera.model.PicSize;
import com.remxcqwphotoo.camera.net.Base64Utils;
import com.remxcqwphotoo.camera.service.callback.ApiCallback;

import java.io.File;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;

/**
 * ID照片生成服务
 * 负责处理证件照生成相关的API调用
 */
public class IdPhotoService {

    private static IdPhotoService instance;
    private final CompositeDisposable compositeDisposable = new CompositeDisposable();

    private IdPhotoService() {
    }

    public static IdPhotoService getInstance() {
        if (instance == null) {
            synchronized (IdPhotoService.class) {
                if (instance == null) {
                    instance = new IdPhotoService();
                }
            }
        }
        return instance;
    }

    /**
     * 生成ID照片
     *
     * @param imagePath 原始图片路径
     * @param picSize   照片尺寸
     * @param callback  回调接口
     */
    public void generateIdPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        generateIdPhoto(imagePath, picSize, new IdPhotoConfig(), callback);
    }

    /**
     * 生成ID照片（带自定义配置）
     *
     * @param imagePath 原始图片路径
     * @param picSize   照片尺寸
     * @param config    生成配置
     * @param callback  回调接口
     */
    @SuppressLint("CheckResult")
    public void generateIdPhoto(String imagePath, PicSize picSize, IdPhotoConfig config, ApiCallback<Bitmap> callback) {
        if (callback != null) {
            callback.onStart();
        }

        File imageFile = new File(imagePath);

        Disposable disposable = RxHttp.postForm(ApiConfig.ID_PHOTO_URL)
                .addHeader("token", ApiConfig.ID_PHOTO_TOKEN)
                .addFile("input_image", imageFile)
                .add("height", String.valueOf(picSize.height))
                .add("width", String.valueOf(picSize.width))
                .add("human_matting_model", config.humanMattingModel)
                .add("face_detect_model", config.faceDetectModel)
                .add("hd", config.hd)
                .add("dpi", config.dpi)
                .add("face_alignment", config.faceAlignment)
                .add("head_height_ratio", config.headHeightRatio)
                .add("head_measure_ratio", config.headMeasureRatio)
                .add("top_distance_min", config.topDistanceMin)
                .add("top_distance_max", config.topDistanceMax)
                .add("sharpen_strength", config.sharpenStrength)
                .add("saturation_strength", config.saturationStrength)
                .add("brightness_strength", config.brightnessStrength)
                .add("contrast_strength", config.contrastStrength)
                .asClass(IdPhotoData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<IdPhotoData>() {
                    @Override
                    public void accept(IdPhotoData idPhotoData) throws Exception {
                        try {
                            if (idPhotoData.status && !TextUtils.isEmpty(idPhotoData.image_base64_standard)) {
                                // 解析base64图片
                                String base64Data = idPhotoData.image_base64_standard;
                                if (base64Data.startsWith("data:image/")) {
                                    base64Data = base64Data.substring(base64Data.indexOf(",") + 1);
                                }
                                Bitmap bitmap = Base64Utils.base64ToImage(base64Data);

                                if (callback != null) {
                                    callback.onSuccess(bitmap);
                                    callback.onComplete();
                                }
                            } else {
                                if (callback != null) {
                                    callback.onError("照片生成失败");
                                    callback.onComplete();
                                }
                            }
                        } catch (Exception e) {
                            if (callback != null) {
                                callback.onError("照片处理失败: " + e.getMessage());
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });

        compositeDisposable.add(disposable);
    }

    /**
     * 清理所有订阅，防止内存泄漏
     * 在不再需要服务时调用（如Activity/Fragment的onDestroy中）
     */
    public void dispose() {
        if (!compositeDisposable.isDisposed()) {
            compositeDisposable.dispose();
        }
    }

    /**
     * 生成仅裁剪的ID照片（不进行背景处理）
     *
     * @param imagePath 原始图片路径
     * @param picSize   照片尺寸
     * @param callback  回调接口
     */
    @SuppressLint("CheckResult")
    public void generateCroppedIdPhoto(String imagePath, PicSize picSize, ApiCallback<Bitmap> callback) {
        if (callback != null) {
            callback.onStart();
        }
        File imageFile = new File(imagePath);
        IdPhotoConfig config = new IdPhotoConfig();
        Disposable disposable = RxHttp.postForm(ApiConfig.ID_PHOTO_CROP_URL)
                .addHeader("token", ApiConfig.ID_PHOTO_TOKEN)
                .addFile("input_image", imageFile)
                .add("height", picSize.height)
                .add("width", picSize.width)
                .add("face_detect_model", config.faceDetectModel)
                .add("hd", config.hd)
                .add("dpi", config.dpi)
                .add("head_height_ratio", config.headHeightRatio)
                .add("head_measure_ratio", config.headMeasureRatio)
                .add("top_distance_min", config.topDistanceMin)
                .add("top_distance_max", config.topDistanceMax)
                .add("sharpen_strength", config.sharpenStrength)
                .add("saturation_strength", config.saturationStrength)
                .add("brightness_strength", config.brightnessStrength)
                .add("contrast_strength", config.contrastStrength)
                .asClass(IdPhotoData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<IdPhotoData>() {
                    @Override
                    public void accept(IdPhotoData idPhotoData) throws Exception {
                        try {
                            if (idPhotoData.status && !TextUtils.isEmpty(idPhotoData.image_base64_standard)) {
                                // 解析base64图片
                                String base64Data = idPhotoData.image_base64_standard;
                                if (base64Data.startsWith("data:image/")) {
                                    base64Data = base64Data.substring(base64Data.indexOf(",") + 1);
                                }
                                Bitmap bitmap = Base64Utils.base64ToImage(base64Data);

                                if (callback != null) {
                                    callback.onSuccess(bitmap);
                                    callback.onComplete();
                                }
                            } else {
                                if (callback != null) {
                                    callback.onError("照片裁剪失败");
                                    callback.onComplete();
                                }
                            }
                        } catch (Exception e) {
                            if (callback != null) {
                                callback.onError("照片处理失败: " + e.getMessage());
                                callback.onComplete();
                            }
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + throwable.getMessage());
                            callback.onComplete();
                        }
                    }
                });

        compositeDisposable.add(disposable);
    }


    /**
     * ID照片生成配置类
     */
    public static class IdPhotoConfig {
        //        public String humanMattingModel = "modnet_photographic_portrait_matting";
        public String humanMattingModel = "hivision_modnet";

        public String faceDetectModel = "retinaface-resnet50";

        //        public String faceDetectModel = "mtcnn";
        public boolean hd = true;
        public String dpi = ApiConfig.DEFAULT_DPI;
        public boolean faceAlignment = true;
        public double headHeightRatio = 0.45;
        public double headMeasureRatio = 0.2;
        public double topDistanceMin = 0.1;
        public double topDistanceMax = 0.12;
        public int sharpenStrength = 0;
        public int saturationStrength = 0;
        public int brightnessStrength = 10;
        public int contrastStrength = 0;

        // 构建器模式
        public IdPhotoConfig setHumanMattingModel(String model) {
            this.humanMattingModel = model;
            return this;
        }

        public IdPhotoConfig setFaceDetectModel(String model) {
            this.faceDetectModel = model;
            return this;
        }

        public IdPhotoConfig setHd(boolean hd) {
            this.hd = hd;
            return this;
        }

        public IdPhotoConfig setDpi(String dpi) {
            this.dpi = dpi;
            return this;
        }

        public IdPhotoConfig setBrightnessStrength(int strength) {
            this.brightnessStrength = strength;
            return this;
        }

        // 可以继续添加其他配置方法...
    }
}
