package com.remxcqwphotoo.camera.service;

/**
 * 证件照相关API配置类
 * 统一管理所有API端点和认证信息
 */
public class ApiConfig {

        private static final String API_HOST = "https://api.juyingnj.com/photo";
//    private static final String API_HOST = "http://*************:8081";

    /*
     * 人脸检测API (新版本)
     */
    public static final String NEW_FACE_DETECT_URL = API_HOST + "/face_detection";
    public static final String NEW_FACE_DETECT_TOKEN = "aabbcc";

    /**
     * ID照片生成API
     */
    public static final String ID_PHOTO_URL = API_HOST + "/idphoto";
    public static final String ID_PHOTO_TOKEN = "aabbcc";
    
    /**
     * 仅裁剪ID照片API（不进行背景处理）
     */
    public static final String ID_PHOTO_CROP_URL = API_HOST + "/idphoto_crop";
    


    /**
     * 6寸排版照片API
     */
    public static final String LAYOUT_PHOTO_URL = API_HOST + "/generate_layout_photos";

    // ========== 百度API配置 (备用) ==========

    /**
     * 百度API密钥配置
     */
    public static final String BAIDU_APP_KEY = "GZ3j7qBdkXQi7AycP3e86Vn7";
    public static final String BAIDU_APP_KEY_MONEY = "bz7tYWQ1hcU52B7dreNFoGDq";
    public static final String BAIDU_APP_SEC = "5415WA7YONFDjfpd4WGPeN4WtwEhj0zV";
    public static final String BAIDU_APP_SEC_MONEY = "3f3wRGawuvVDLpyGGf5cMcNmSZdHefd7";

    /**
     * 百度API端点
     */
    public static final String BAIDU_BASE_URL = "https://aip.baidubce.com";
    public static final String BAIDU_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s";
    public static final String BAIDU_BODY_DETECT_URL = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_analysis";
    public static final String BAIDU_BODY_SEG_URL = "https://aip.baidubce.com/rest/2.0/image-classify/v1/body_seg";

    /**
     * 获取百度Token URL
     */
    public static String getBaiduTokenUrl(boolean useFree) {
        if (useFree) {
            return String.format(BAIDU_TOKEN_URL, BAIDU_APP_KEY, BAIDU_APP_SEC);
        }
        return String.format(BAIDU_TOKEN_URL, BAIDU_APP_KEY_MONEY, BAIDU_APP_SEC_MONEY);
    }

    // ========== 通用配置 ==========

    /**
     * 默认DPI设置
     */
    public static final String DEFAULT_DPI = "300";

    /**
     * 默认文件大小限制(KB)
     */
    public static final String DEFAULT_KB_LIMIT = "200";

    /**
     * 默认图片质量
     */
    public static final String DEFAULT_QUALITY = "high";

    /**
     * 预览图片质量
     */
    public static final String PREVIEW_QUALITY = "preview";

    // ========== 人脸检测模型配置 ==========

    /**
     * 人脸检测模型选项
     */
    public static final String FACE_MODEL_MTCNN = "mtcnn";
    public static final String FACE_MODEL_RETINAFACE = "retinaface-resnet50";
    public static final String FACE_MODEL_FACEPP = "face++";

    /**
     * 默认人脸检测模型
     */
    public static final String DEFAULT_FACE_MODEL = FACE_MODEL_MTCNN;

    // ========== 相纸尺寸配置 ==========

    /**
     * 相纸尺寸选项
     */
    public static final String PAPER_SIZE_6_INCH = "6 inch";
    public static final String PAPER_SIZE_5_INCH = "5 inch";
    public static final String PAPER_SIZE_A4 = "A4";
    public static final String PAPER_SIZE_3R = "3R";
    public static final String PAPER_SIZE_4R = "4R";

    /**
     * 默认相纸尺寸
     */
    public static final String DEFAULT_PAPER_SIZE = PAPER_SIZE_6_INCH;
}
