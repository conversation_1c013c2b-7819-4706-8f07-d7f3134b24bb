apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'android-junk-code'


def packageName = "com.remxcqwphotoo.camera"
def appName = "免费证件照王"
def appName_vivo = "免费证件照"
def appOwner = "陶陶文化艺术（南京）有限公司"
def junkPrefix = "remxcqwphotoo_"
def appName_xiaomi = "免费证件照相机"
def appName_oppo = "免费证件照换背景"

def packageName_yyb = "com.yybremxcqwphotoo.camera"
def appName_yyb = "免费证件照"


android {

    lint {
        baseline = file("lint-baseline.xml")
    }
    signingConfigs {
//        release {
//            storeFile file(new File(project.rootDir.absolutePath, "zjzv4_yyb.jks").absolutePath)
//            storePassword 'qwerasdf'
//            keyAlias = 'key0'
//            keyPassword 'qwerasdf'
//        }
//        test {
//            storeFile file(new File(project.rootDir.absolutePath, "zjzv4_yyb.jks").absolutePath)
//            storePassword 'qwerasdf'
//            keyAlias = 'key0'
//            keyPassword 'qwerasdf'
//        }
        release {
            storeFile file(new File(project.rootDir.absolutePath, "zjz4_key.jks").absolutePath)
            storePassword '111111'
            keyAlias = 'key0'
            keyPassword '123456'
        }
        test {
            storeFile file(new File(project.rootDir.absolutePath, "zjz4_key.jks").absolutePath)
            storePassword '111111'
            keyAlias = 'key0'
            keyPassword '123456'
        }
//        test {
//            storeFile file(new File(project.rootDir.absolutePath, "app_key.jks").absolutePath)
//            storePassword '123456'
//            keyAlias = 'key0'
//            keyPassword '123456'
//        }
    }
    compileSdkVersion 34
    buildToolsVersion "30.0.3"
    defaultConfig {
        applicationId "com.remxcqwphotoo.camera"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1001
        versionName "1.0.1"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        rxhttp_package: 'rxhttp',   //非必须，指定RxHttp类包名
                        rxhttp_rxjava : 'rxjava3'
                ]
            }
        }
//        ndk {
//            abiFilters "armeabi-v7a"
//        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility = 1.8
        targetCompatibility = 1.8
    }
    flavorDimensions 'appstore'
    productFlavors {
        huawei {
            dimension = 'appstore'
            applicationId packageName
            versionCode = 5007
            versionName = '5.0.7'
            manifestPlaceholders = [
                    icon   : "@mipmap/ic_logo",
                    appName: appName
            ]
            buildConfigField("String", "APP_NAME", "\"${appName}\"")
            buildConfigField("String", "OWNER", "\"${appOwner}\"")
            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
            buildConfigField("String", "UMENG_APP_CHANNEL", "\"huawei${appName}\"")
            buildConfigField("boolean", "IP_LOC", "false")
        }

        honor {
            dimension = 'appstore'
            applicationId packageName
            versionCode = 5007
            versionName = '5.0.7'
            manifestPlaceholders = [
                    icon   : "@mipmap/ic_logo",
                    appName: appName
            ]
            buildConfigField("String", "APP_NAME", "\"${appName}\"")
            buildConfigField("String", "OWNER", "\"${appOwner}\"")
            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
            buildConfigField("String", "UMENG_APP_CHANNEL", "\"honor${appName}\"")
            buildConfigField("boolean", "IP_LOC", "false")
        }

        oppo {
            dimension = 'appstore'
            applicationId packageName
            versionCode = 5004
            versionName = '5.0.4'
            manifestPlaceholders = [
                    icon   : "@mipmap/ic_logo",
                    appName: appName
            ]
            buildConfigField("String", "APP_NAME", "\"${appName}\"")
            buildConfigField("String", "OWNER", "\"${appOwner}\"")
            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
            buildConfigField("String", "UMENG_APP_CHANNEL", "\"oppo${appName}\"")
        }

//        vivo {
//            dimension = 'appstore'
//            applicationId packageName
//            versionCode = 5002
//            versionName = '5.0.2'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_vivo
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_vivo}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"vivo${appName_vivo}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }


//        xiaomi {
//            dimension = 'appstore'
//            applicationId packageName
//            versionCode = 5001
//            versionName = '5.0.1'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_xiaomi
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_xiaomi}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"xiaomi${appName_xiaomi}\"")
//        }
//        vivo {
//            dimension = 'appstore'
//            applicationId packageName
//            versionCode = 1009
//            versionName = '1.0.9'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_vivo
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_vivo}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"vivo${appName_vivo}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }

//        oppo {
//            dimension = 'appstore'
//            applicationId packageName
//            versionCode = 1009
//            versionName = '1.0.9'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_oppo
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_oppo}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"oppo${appName_oppo}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }

//        xiaomi {
//            dimension = 'appstore'
//            applicationId packageName
//            versionCode = 1009
//            versionName = '1.0.9'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_xiaomi
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_xiaomi}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"xiaomi${appName_xiaomi}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }
//        baidu {
//            dimension = 'appstore'
//            applicationId packageName_yyb
//            versionCode = 1009
//            versionName = '1.0.9'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_yyb
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_yyb}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"baidu${appName_yyb}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }

//        yyb {
//            dimension = 'appstore'
//            applicationId packageName_yyb
//            versionCode = 1009
//            versionName = '1.0.9'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: appName_yyb
//            ]
//            buildConfigField("String", "APP_NAME", "\"${appName_yyb}\"")
//            buildConfigField("String", "OWNER", "\"${appOwner}\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"605cc95a6ee47d382b96e2a6\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"yyb${appName_yyb}\"")
//            buildConfigField("boolean", "IP_LOC", "false")
//        }
//        xiaomi {
//            dimension = 'appstore'
//            applicationId "com.filterpic.camera"
//            versionCode = 1007
//            versionName = '1.0.7'
//            manifestPlaceholders = [
//                    icon   : "@mipmap/ic_logo",
//                    appName: "免费证件照拍照"
//            ]
//            buildConfigField("String", "APP_NAME", "\"免费证件照拍照\"")
//            buildConfigField("String", "UMENG_APP_KEY", "\"601013056a2a470e8f8d6132\"")
//            buildConfigField("String", "UMENG_APP_CHANNEL", "\"xiaomi\"")
//            buildConfigField("String", "CP_ID", "\"c1001\"")
//            buildConfigField("String", "CH_ID", "\"huawei20191121\"")
//        }
//        xiaomi_1 {
//            dimension = 'appstore'
//            applicationId "com.remxcqwphotoo.camera"
//            versionCode = 1002
//            versionName = '1.0.2'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_logo",
//                    appName: "证件照免费版"
//            ]
//            buildConfigField("String","APP_NAME","\"证件照免费版\"")
//            buildConfigField("String","UMENG_APP_KEY","\"601013056a2a470e8f8d6132\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"xiaomi\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"xiaomi20191121\"")
////            signingConfig signingConfigs.test
//        }
//        huawei_2 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera2"
//            versionCode = 1000
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/icon1_512",
//                    appName: "证件照"
//            ]
//            buildConfigField("String","APP_NAME","\"证件照\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09db4ca357ec5d000ce1\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"huawei\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"huawei20191125\"")
//        }
//        oppo1 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera"
//            versionCode = 1001
//            versionName = '1.0.1'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_launcher_zjz_2",
//                    appName: "最美一寸照"
//            ]
//            buildConfigField("String","APP_NAME","\"最美一寸照\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09944ca357d66e000336\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"oppo\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"oppo20200223\"")
//        }
//        vivo_1 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera"
//            versionCode = 1
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_launcher_zjz_1",
//                    appName: "证件照免费"
//            ]
//            buildConfigField("String","APP_NAME","\"证件照免费\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09944ca357d66e000336\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"vivo\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"zjz\"")
//        }
//        vivo_2 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera2"
//            versionCode = 1
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_launcher_zjz_2",
//                    appName: "一寸照"
//            ]
//            buildConfigField("String","APP_NAME","\"一寸照\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09db4ca357ec5d000ce1\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"vivo\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"zjz\"")
//        }
//        xiaomi_1 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera"
//            versionCode = 1
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_launcher_zjz_2",
//                    appName: "一寸照"
//            ]
//            buildConfigField("String","APP_NAME","\"一寸照\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09944ca357d66e000336\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"xiaomi\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"xiaomi20191120\"")
//        }
//        xiaomi_2 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera2"
//            versionCode = 1
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/ic_launcher_zjz_2",
//                    appName: "一寸照"
//            ]
//            buildConfigField("String","APP_NAME","\"一寸照\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09db4ca357ec5d000ce1\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"xiaomi\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"xiaomi20191111\"")
//        }
//        meizu_1 {
//            dimension = 'appstore'
//            applicationId "com.cttz.zjzcamera"
//            versionCode = 1
//            versionName = '1.0.0'
//            manifestPlaceholders = [
//                    icon: "@mipmap/icon1_512",
//                    appName: "证件照相机"
//            ]
//            buildConfigField("String","APP_NAME","\"证件照相机\"")
//            buildConfigField("String","UMENG_APP_KEY","\"5dac09944ca357d66e000336\"")
//            buildConfigField("String","UMENG_APP_CHANNEL","\"meizu\"")
//            buildConfigField("String","CP_ID","\"c1001\"")
//            buildConfigField("String","CH_ID","\"meizu20191112\"")
//        }
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

android.applicationVariants.all { variant ->
    if (variant.name.contains("Release")) {
        androidJunkCode.configMap.put(variant.name, {
            packageBase = packageName  //生成java类根包名
            packageCount = 30 //生成包数量
            activityCountPerPackage = 6 //每个包下生成Activity类数量
            excludeActivityJavaFile = false
            //是否排除生成Activity的Java文件,默认false(layout和写入AndroidManifest.xml还会执行)，主要用于处理类似神策全埋点编译过慢问题
            otherCountPerPackage = 50  //每个包下生成其它类的数量
            methodCountPerClass = 22  //每个类下生成方法数量
            resPrefix = junkPrefix  //生成的layout、drawable、string等资源名前缀
            drawableCount = 301  //生成drawable资源数量
            stringCount = 303  //生成string数量
        })
    }
}


//configurations {
//    all {
//        exclude module: 'httpclient'
//        exclude module: 'commons-logging'
//    }
//}


//configurations.all {
//    resolutionStrategy {
//        force 'androidx.core:core-ktx:1.3.1'
//    }
//}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.core:core-ktx:1.0.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.otaliastudios:cameraview:2.2.0'
    implementation 'com.google.android.material:material:1.1.0-alpha03'
    implementation "androidx.recyclerview:recyclerview:1.0.0"
    implementation 'com.github.bumptech.glide:glide:4.7.1'
    implementation 'com.squareup.picasso:picasso:2.5.2'
    implementation project(':cameraview')
    implementation 'top.zibin:Luban:1.1.8'
    implementation 'com.qmuiteam:qmui:1.4.0'
    implementation "com.qmuiteam:arch:0.6.1"
    implementation 'com.jakewharton:butterknife:10.2.1'
    kapt 'com.jakewharton:butterknife-compiler:10.2.1'
    implementation 'me.jessyan:autosize:1.1.2'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    implementation 'com.just.agentweb:agentweb:4.1.2'
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.0'
    implementation 'com.umeng.umsdk:common:9.6.7'// 必选
    implementation 'com.umeng.umsdk:asms:1.8.2'// 必选
    implementation 'com.umeng.umsdk:apm:1.9.5' //
    implementation 'jp.co.cyberagent.android:gpuimage:2.1.0'
    implementation 'io.github.lucksiege:pictureselector:v3.11.1'
    implementation 'com.blankj:utilcodex:1.31.0'
    implementation 'com.github.liujingxing.rxhttp:rxhttp:2.6.1'
    implementation 'com.squareup.okhttp3:okhttp:4.9.0' //rxhttp v2.2.2版本起，需要手动依赖okhttp
    kapt 'com.github.liujingxing.rxhttp:rxhttp-compiler:2.6.2'
    implementation 'com.github.liujingxing.rxlife:rxlife-coroutine:2.1.0' //管理协程生命周期，页面销毁，关闭请求
    implementation 'io.reactivex.rxjava3:rxjava:3.0.6'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'com.github.liujingxing.rxlife:rxlife-rxjava3:2.1.0' //管理RxJava3生命周期，页面销毁，关闭请求

    //非必须，根据自己需求选择 RxHttp默认内置了GsonConverter
    implementation 'com.github.liujingxing.rxhttp:converter-fastjson:2.6.1'
    implementation 'com.github.liujingxing.rxhttp:converter-jackson:2.6.1'
    implementation 'com.github.liujingxing.rxhttp:converter-moshi:2.6.1'
    implementation 'com.github.liujingxing.rxhttp:converter-protobuf:2.6.1'
    implementation 'com.github.liujingxing.rxhttp:converter-simplexml:2.6.1'
    implementation 'com.qq.e.union:union:4.640.1510'

}
